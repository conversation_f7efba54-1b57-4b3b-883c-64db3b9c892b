{"ast": null, "code": "'use strict';\n\nvar NATIVE_BIND = require('../internals/function-bind-native');\nvar call = Function.prototype.call;\n// eslint-disable-next-line es/no-function-prototype-bind -- safe\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};", "map": {"version": 3, "names": ["NATIVE_BIND", "require", "call", "Function", "prototype", "module", "exports", "bind", "apply", "arguments"], "sources": ["C:/tictetmigration/frontend/node_modules/core-js-pure/internals/function-call.js"], "sourcesContent": ["'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar call = Function.prototype.call;\n// eslint-disable-next-line es/no-function-prototype-bind -- safe\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAE9D,IAAIC,IAAI,GAAGC,QAAQ,CAACC,SAAS,CAACF,IAAI;AAClC;AACAG,MAAM,CAACC,OAAO,GAAGN,WAAW,GAAGE,IAAI,CAACK,IAAI,CAACL,IAAI,CAAC,GAAG,YAAY;EAC3D,OAAOA,IAAI,CAACM,KAAK,CAACN,IAAI,EAAEO,SAAS,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}