import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  Checkbox,
  ListItemIcon
} from '@mui/material';
import {
  Save as SaveIcon,
  TestTube as TestIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';

import { configApi, handleApiError } from '../services/api';

const Configuration = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState({ source: false, target: false });
  const [config, setConfig] = useState({
    sourceUrl: 'https://kambaa.freshdesk.com/api/v2',
    sourceApiKey: 'LgcukXyOv4B7sAzRQcI',
    targetUrl: '',
    targetApiKey: '',
    batchSize: 50,
    rateLimit: 1000,
    maxRetries: 3,
    dateRangeFrom: '2020-01-01',
    dateRangeTo: new Date().toISOString().split('T')[0],
    ticketTypes: [
      'CAH',
      'PWC-SAKS Global',
      'IPG',
      'Inspire Brands',
      'Costa Forms',
      'Robertshaw',
      'Albert Heijn',
      'HACH',
      'Uline',
      'Accuride'
    ]
  });
  const [testResults, setTestResults] = useState({
    source: null,
    target: null
  });
  const [apiInfo, setApiInfo] = useState(null);

  useEffect(() => {
    loadConfiguration();
    loadApiInfo();
  }, []);

  const loadConfiguration = async () => {
    try {
      setLoading(true);
      const response = await configApi.get();
      if (response.data.config) {
        setConfig(prev => ({ ...prev, ...response.data.config }));
      }
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to load configuration: ${errorInfo.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadApiInfo = async () => {
    try {
      const response = await configApi.getApiInfo('freshdesk');
      setApiInfo(response.data.apiInfo);
    } catch (err) {
      console.error('Failed to load API info:', err);
    }
  };

  const handleInputChange = (field) => (event) => {
    const value = event.target.type === 'number' ?
      parseInt(event.target.value) || 0 :
      event.target.value;

    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTicketTypesChange = (event) => {
    const value = event.target.value;
    setConfig(prev => ({
      ...prev,
      ticketTypes: typeof value === 'string' ? value.split(',') : value
    }));
  };

  const availableTicketTypes = [
    'CAH',
    'PWC-SAKS Global',
    'IPG',
    'Inspire Brands',
    'Costa Forms',
    'Robertshaw',
    'Albert Heijn',
    'HACH',
    'Uline',
    'Accuride'
  ];

  const testConnection = async (type) => {
    const isSource = type === 'source';
    const apiUrl = isSource ? config.sourceUrl : config.targetUrl;
    const apiKey = isSource ? config.sourceApiKey : config.targetApiKey;

    if (!apiUrl || !apiKey) {
      toast.error(`Please enter both URL and API key for ${type}`);
      return;
    }

    setTesting(prev => ({ ...prev, [type]: true }));
    setTestResults(prev => ({ ...prev, [type]: null }));

    try {
      const response = await configApi.testConnection({
        apiUrl,
        apiKey,
        type: 'freshdesk'
      });

      setTestResults(prev => ({
        ...prev,
        [type]: {
          success: true,
          message: response.data.message,
          status: response.data.status,
          responseTime: response.data.responseTime
        }
      }));

      toast.success(`${type} connection successful!`);

    } catch (err) {
      const errorInfo = handleApiError(err);
      setTestResults(prev => ({
        ...prev,
        [type]: {
          success: false,
          message: errorInfo.message,
          details: errorInfo.details
        }
      }));

      toast.error(`${type} connection failed: ${errorInfo.message}`);
    } finally {
      setTesting(prev => ({ ...prev, [type]: false }));
    }
  };

  const saveConfiguration = async () => {
    setSaving(true);
    try {
      await configApi.update(config);
      toast.success('Configuration saved successfully!');
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to save configuration: ${errorInfo.message}`);
    } finally {
      setSaving(false);
    }
  };

  const renderTestResult = (result) => {
    if (!result) return null;

    return (
      <Alert 
        severity={result.success ? 'success' : 'error'} 
        sx={{ mt: 1 }}
        icon={result.success ? <SuccessIcon /> : <ErrorIcon />}
      >
        <Typography variant="body2">
          {result.message}
        </Typography>
        {result.status && (
          <Typography variant="caption" display="block">
            Status: {result.status}
            {result.responseTime && ` | Response Time: ${result.responseTime}`}
          </Typography>
        )}
        {result.details && (
          <Typography variant="caption" display="block" color="error">
            Details: {result.details}
          </Typography>
        )}
      </Alert>
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        API Configuration
      </Typography>

      <Typography variant="body1" color="textSecondary" paragraph>
        Configure your source and target API credentials for ticket migration.
      </Typography>

      <Grid container spacing={3}>
        {/* Source API Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                Source API (Freshdesk)
              </Typography>
              
              <TextField
                fullWidth
                label="Source API URL"
                value={config.sourceUrl}
                onChange={handleInputChange('sourceUrl')}
                placeholder="https://your-domain.freshdesk.com/api/v2"
                margin="normal"
                helperText="Your source Freshdesk API URL"
              />
              
              <TextField
                fullWidth
                label="Source API Key"
                type="password"
                value={config.sourceApiKey}
                onChange={handleInputChange('sourceApiKey')}
                placeholder="Your API key"
                margin="normal"
                helperText="Your source Freshdesk API key"
              />

              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={testing.source ? <CircularProgress size={16} /> : <TestIcon />}
                  onClick={() => testConnection('source')}
                  disabled={testing.source}
                  fullWidth
                >
                  {testing.source ? 'Testing...' : 'Test Source Connection'}
                </Button>
                {renderTestResult(testResults.source)}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Target API Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="secondary">
                Target API (Freshdesk)
              </Typography>
              
              <TextField
                fullWidth
                label="Target API URL"
                value={config.targetUrl}
                onChange={handleInputChange('targetUrl')}
                placeholder="https://your-target-domain.freshdesk.com/api/v2"
                margin="normal"
                helperText="Your target Freshdesk API URL"
              />
              
              <TextField
                fullWidth
                label="Target API Key"
                type="password"
                value={config.targetApiKey}
                onChange={handleInputChange('targetApiKey')}
                placeholder="Your API key"
                margin="normal"
                helperText="Your target Freshdesk API key"
              />

              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={testing.target ? <CircularProgress size={16} /> : <TestIcon />}
                  onClick={() => testConnection('target')}
                  disabled={testing.target}
                  fullWidth
                >
                  {testing.target ? 'Testing...' : 'Test Target Connection'}
                </Button>
                {renderTestResult(testResults.target)}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Migration Settings */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Migration Settings
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Batch Size"
                    type="number"
                    value={config.batchSize}
                    onChange={handleInputChange('batchSize')}
                    margin="normal"
                    helperText="Number of tickets to process in each batch"
                    inputProps={{ min: 1, max: 100 }}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Rate Limit (ms)"
                    type="number"
                    value={config.rateLimit}
                    onChange={handleInputChange('rateLimit')}
                    margin="normal"
                    helperText="Delay between API calls in milliseconds"
                    inputProps={{ min: 100, max: 10000 }}
                  />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Max Retries"
                    type="number"
                    value={config.maxRetries}
                    onChange={handleInputChange('maxRetries')}
                    margin="normal"
                    helperText="Maximum retry attempts for failed requests"
                    inputProps={{ min: 0, max: 10 }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Migration Filters */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Migration Filters
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Date Range From"
                    type="date"
                    value={config.dateRangeFrom}
                    onChange={handleInputChange('dateRangeFrom')}
                    margin="normal"
                    helperText="Start date for ticket migration (default: 2020-01-01)"
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Date Range To"
                    type="date"
                    value={config.dateRangeTo}
                    onChange={handleInputChange('dateRangeTo')}
                    margin="normal"
                    helperText="End date for ticket migration (default: today)"
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Ticket Types to Include</InputLabel>
                    <Select
                      multiple
                      value={config.ticketTypes}
                      onChange={handleTicketTypesChange}
                      input={<OutlinedInput label="Ticket Types to Include" />}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      {availableTicketTypes.map((type) => (
                        <MenuItem key={type} value={type}>
                          <Checkbox checked={config.ticketTypes.indexOf(type) > -1} />
                          <ListItemText primary={type} />
                        </MenuItem>
                      ))}
                    </Select>
                    <Typography variant="caption" color="textSecondary" sx={{ mt: 1 }}>
                      Select specific ticket types to migrate. Only tickets matching these types will be processed.
                    </Typography>
                  </FormControl>
                </Grid>
              </Grid>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Migration Strategy:</strong> Tickets will be fetched month by month from {config.dateRangeFrom} to {config.dateRangeTo}
                  to handle the 300 ticket per request limit. This ensures all tickets are captured without hitting API limits.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        {/* API Information */}
        {apiInfo && (
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">
                  <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Freshdesk API Information
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Configuration Notes:
                    </Typography>
                    <List dense>
                      {apiInfo.notes?.map((note, index) => (
                        <ListItem key={index}>
                          <ListItemText primary={note} />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      API Details:
                    </Typography>
                    <Box sx={{ mb: 1 }}>
                      <Chip label={`Rate Limit: ${apiInfo.rateLimit}`} size="small" />
                    </Box>
                    <Typography variant="body2" color="textSecondary">
                      Documentation: {' '}
                      <a href={apiInfo.documentation} target="_blank" rel="noopener noreferrer">
                        {apiInfo.documentation}
                      </a>
                    </Typography>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        )}

        {/* Save Button */}
        <Grid item xs={12}>
          <Box display="flex" justifyContent="center">
            <Button
              variant="contained"
              size="large"
              startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
              onClick={saveConfiguration}
              disabled={saving}
              sx={{ minWidth: 200 }}
            >
              {saving ? 'Saving...' : 'Save Configuration'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Configuration;
