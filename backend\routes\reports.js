const express = require('express');
const logger = require('../utils/logger');
const { getDatabase } = require('../database/init');

const router = express.Router();

// Get migration report
router.get('/:migrationId', async (req, res) => {
  try {
    const { migrationId } = req.params;
    const db = getDatabase();

    // Get migration details
    const migration = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM migrations WHERE id = ?',
        [migrationId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (!migration) {
      return res.status(404).json({
        error: 'Migration not found'
      });
    }

    // Get ticket migration details
    const tickets = await new Promise((resolve, reject) => {
      db.all(
        `SELECT tm.*, 
                COUNT(cm.id) as conversation_count,
                COUNT(am.id) as attachment_count
         FROM ticket_migrations tm
         LEFT JOIN conversation_migrations cm ON tm.id = cm.ticket_migration_id
         LEFT JOIN attachment_migrations am ON cm.id = am.conversation_migration_id
         WHERE tm.migration_id = ?
         GROUP BY tm.id
         ORDER BY tm.created_at`,
        [migrationId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get failed tickets with details
    const failedTickets = await new Promise((resolve, reject) => {
      db.all(
        `SELECT source_ticket_id, error_message, retry_count, created_at
         FROM ticket_migrations 
         WHERE migration_id = ? AND status = 'failed'
         ORDER BY created_at`,
        [migrationId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get conversation migration stats
    const conversationStats = await new Promise((resolve, reject) => {
      db.all(
        `SELECT cm.status, COUNT(*) as count
         FROM conversation_migrations cm
         JOIN ticket_migrations tm ON cm.ticket_migration_id = tm.id
         WHERE tm.migration_id = ?
         GROUP BY cm.status`,
        [migrationId],
        (err, rows) => {
          if (err) reject(err);
          else {
            const stats = {};
            rows.forEach(row => {
              stats[row.status] = row.count;
            });
            resolve(stats);
          }
        }
      );
    });

    // Calculate statistics
    const stats = {
      total: migration.total_tickets || 0,
      processed: migration.processed_tickets || 0,
      successful: migration.successful_tickets || 0,
      failed: migration.failed_tickets || 0,
      pending: Math.max(0, (migration.total_tickets || 0) - (migration.processed_tickets || 0)),
      successRate: migration.processed_tickets > 0 ? 
        ((migration.successful_tickets || 0) / migration.processed_tickets * 100).toFixed(2) : 0,
      conversations: conversationStats
    };

    // Calculate duration
    let duration = null;
    if (migration.started_at && migration.completed_at) {
      const start = new Date(migration.started_at);
      const end = new Date(migration.completed_at);
      duration = Math.round((end - start) / 1000); // Duration in seconds
    }

    res.json({
      migration: {
        ...migration,
        duration
      },
      stats,
      tickets,
      failedTickets,
      conversationStats
    });

  } catch (error) {
    logger.error('Error getting migration report:', error);
    res.status(500).json({
      error: 'Failed to get migration report',
      message: error.message
    });
  }
});

// Get summary reports for all migrations
router.get('/', async (req, res) => {
  try {
    const db = getDatabase();
    const { startDate, endDate, status } = req.query;

    let whereClause = '1=1';
    const params = [];

    if (startDate) {
      whereClause += ' AND created_at >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND created_at <= ?';
      params.push(endDate);
    }

    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }

    // Get migration summaries
    const migrations = await new Promise((resolve, reject) => {
      db.all(
        `SELECT id, source_url, target_url, status, total_tickets,
                successful_tickets, failed_tickets, created_at, completed_at
         FROM migrations 
         WHERE ${whereClause}
         ORDER BY created_at DESC`,
        params,
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Get overall statistics
    const overallStats = await new Promise((resolve, reject) => {
      db.get(
        `SELECT 
           COUNT(*) as total_migrations,
           SUM(total_tickets) as total_tickets,
           SUM(successful_tickets) as total_successful,
           SUM(failed_tickets) as total_failed,
           AVG(CASE WHEN status = 'completed' THEN 
             (julianday(completed_at) - julianday(started_at)) * 24 * 60 * 60 
           END) as avg_duration_seconds
         FROM migrations 
         WHERE ${whereClause}`,
        params,
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    // Get status distribution
    const statusDistribution = await new Promise((resolve, reject) => {
      db.all(
        `SELECT status, COUNT(*) as count
         FROM migrations 
         WHERE ${whereClause}
         GROUP BY status`,
        params,
        (err, rows) => {
          if (err) reject(err);
          else {
            const distribution = {};
            rows.forEach(row => {
              distribution[row.status] = row.count;
            });
            resolve(distribution);
          }
        }
      );
    });

    res.json({
      migrations,
      overallStats: {
        ...overallStats,
        successRate: overallStats.total_tickets > 0 ? 
          ((overallStats.total_successful || 0) / overallStats.total_tickets * 100).toFixed(2) : 0,
        avgDurationMinutes: overallStats.avg_duration_seconds ? 
          Math.round(overallStats.avg_duration_seconds / 60) : null
      },
      statusDistribution
    });

  } catch (error) {
    logger.error('Error getting migration reports:', error);
    res.status(500).json({
      error: 'Failed to get migration reports',
      message: error.message
    });
  }
});

// Export migration report as CSV
router.get('/:migrationId/export', async (req, res) => {
  try {
    const { migrationId } = req.params;
    const db = getDatabase();

    // Get detailed ticket data
    const tickets = await new Promise((resolve, reject) => {
      db.all(
        `SELECT tm.source_ticket_id, tm.target_ticket_id, tm.status,
                tm.error_message, tm.retry_count, tm.created_at, tm.migrated_at
         FROM ticket_migrations tm
         WHERE tm.migration_id = ?
         ORDER BY tm.created_at`,
        [migrationId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    // Generate CSV content
    const csvHeaders = [
      'Source Ticket ID',
      'Target Ticket ID',
      'Status',
      'Error Message',
      'Retry Count',
      'Created At',
      'Migrated At'
    ];

    const csvRows = tickets.map(ticket => [
      ticket.source_ticket_id,
      ticket.target_ticket_id || '',
      ticket.status,
      ticket.error_message || '',
      ticket.retry_count,
      ticket.created_at,
      ticket.migrated_at || ''
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="migration-${migrationId}-report.csv"`);
    res.send(csvContent);

  } catch (error) {
    logger.error('Error exporting migration report:', error);
    res.status(500).json({
      error: 'Failed to export migration report',
      message: error.message
    });
  }
});

module.exports = router;
