{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getGrid2UtilityClass(slot) {\n  return generateUtilityClass('MuiGrid2', slot);\n}\nconst SPACINGS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\nconst DIRECTIONS = ['column-reverse', 'column', 'row-reverse', 'row'];\nconst WRAPS = ['nowrap', 'wrap-reverse', 'wrap'];\nconst GRID_SIZES = ['auto', true, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\nconst grid2Classes = generateUtilityClasses('MuiGrid2', ['root', 'container', 'item', 'zeroMinWidth',\n// spacings\n...SPACINGS.map(spacing => `spacing-xs-${spacing}`),\n// direction values\n...DIRECTIONS.map(direction => `direction-xs-${direction}`),\n// wrap values\n...WRAPS.map(wrap => `wrap-xs-${wrap}`),\n// grid sizes for all breakpoints\n...GRID_SIZES.map(size => `grid-xs-${size}`), ...GRID_SIZES.map(size => `grid-sm-${size}`), ...GRID_SIZES.map(size => `grid-md-${size}`), ...GRID_SIZES.map(size => `grid-lg-${size}`), ...GRID_SIZES.map(size => `grid-xl-${size}`)]);\nexport default grid2Classes;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getGrid2UtilityClass", "slot", "SPACINGS", "DIRECTIONS", "WRAPS", "GRID_SIZES", "grid2Classes", "map", "spacing", "direction", "wrap", "size"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/Unstable_Grid2/grid2Classes.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getGrid2UtilityClass(slot) {\n  return generateUtilityClass('MuiGrid2', slot);\n}\nconst SPACINGS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\nconst DIRECTIONS = ['column-reverse', 'column', 'row-reverse', 'row'];\nconst WRAPS = ['nowrap', 'wrap-reverse', 'wrap'];\nconst GRID_SIZES = ['auto', true, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\nconst grid2Classes = generateUtilityClasses('MuiGrid2', ['root', 'container', 'item', 'zeroMinWidth',\n// spacings\n...SPACINGS.map(spacing => `spacing-xs-${spacing}`),\n// direction values\n...DIRECTIONS.map(direction => `direction-xs-${direction}`),\n// wrap values\n...WRAPS.map(wrap => `wrap-xs-${wrap}`),\n// grid sizes for all breakpoints\n...GRID_SIZES.map(size => `grid-xs-${size}`), ...GRID_SIZES.map(size => `grid-sm-${size}`), ...GRID_SIZES.map(size => `grid-md-${size}`), ...GRID_SIZES.map(size => `grid-lg-${size}`), ...GRID_SIZES.map(size => `grid-xl-${size}`)]);\nexport default grid2Classes;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOF,oBAAoB,CAAC,UAAU,EAAEE,IAAI,CAAC;AAC/C;AACA,MAAMC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;AACnD,MAAMC,UAAU,GAAG,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC;AACrE,MAAMC,KAAK,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC;AAChD,MAAMC,UAAU,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACxE,MAAMC,YAAY,GAAGR,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc;AACpG;AACA,GAAGI,QAAQ,CAACK,GAAG,CAACC,OAAO,IAAI,cAAcA,OAAO,EAAE,CAAC;AACnD;AACA,GAAGL,UAAU,CAACI,GAAG,CAACE,SAAS,IAAI,gBAAgBA,SAAS,EAAE,CAAC;AAC3D;AACA,GAAGL,KAAK,CAACG,GAAG,CAACG,IAAI,IAAI,WAAWA,IAAI,EAAE,CAAC;AACvC;AACA,GAAGL,UAAU,CAACE,GAAG,CAACI,IAAI,IAAI,WAAWA,IAAI,EAAE,CAAC,EAAE,GAAGN,UAAU,CAACE,GAAG,CAACI,IAAI,IAAI,WAAWA,IAAI,EAAE,CAAC,EAAE,GAAGN,UAAU,CAACE,GAAG,CAACI,IAAI,IAAI,WAAWA,IAAI,EAAE,CAAC,EAAE,GAAGN,UAAU,CAACE,GAAG,CAACI,IAAI,IAAI,WAAWA,IAAI,EAAE,CAAC,EAAE,GAAGN,UAAU,CAACE,GAAG,CAACI,IAAI,IAAI,WAAWA,IAAI,EAAE,CAAC,CAAC,CAAC;AACtO,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}