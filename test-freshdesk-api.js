const axios = require('axios');

async function testFreshdeskAPI() {
  console.log('🔍 Testing Freshdesk API Parameters...\n');
  
  const sourceApi = axios.create({
    baseURL: 'https://kambaa.freshdesk.com/api/v2',
    auth: {
      username: 'LgcukXyOv4B7sAzRQcI',
      password: 'X'
    },
    timeout: 10000
  });

  // Test 1: Basic tickets endpoint
  try {
    console.log('📡 Test 1: Basic tickets endpoint...');
    const response = await sourceApi.get('/tickets?per_page=5');
    console.log(`✅ Success: ${response.status} - Found ${response.data.length} tickets`);
    
    if (response.data.length > 0) {
      const ticket = response.data[0];
      console.log(`📋 Sample ticket:`, {
        id: ticket.id,
        subject: ticket.subject,
        created_at: ticket.created_at,
        type: ticket.type,
        custom_fields: ticket.custom_fields
      });
    }
  } catch (error) {
    console.error('❌ Test 1 Failed:', error.response?.status, error.response?.statusText);
  }

  // Test 2: With updated_since parameter
  try {
    console.log('\n📡 Test 2: With updated_since parameter...');
    const response = await sourceApi.get('/tickets', {
      params: {
        per_page: 5,
        updated_since: '2024-01-01T00:00:00Z'
      }
    });
    console.log(`✅ Success: ${response.status} - Found ${response.data.length} tickets`);
  } catch (error) {
    console.error('❌ Test 2 Failed:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.error('Error details:', error.response.data);
    }
  }

  // Test 3: Simple pagination
  try {
    console.log('\n📡 Test 3: Simple pagination...');
    const response = await sourceApi.get('/tickets', {
      params: {
        page: 1,
        per_page: 10
      }
    });
    console.log(`✅ Success: ${response.status} - Found ${response.data.length} tickets`);
  } catch (error) {
    console.error('❌ Test 3 Failed:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.error('Error details:', error.response.data);
    }
  }

  // Test 4: Check ticket structure for filtering
  try {
    console.log('\n📡 Test 4: Analyzing ticket structure...');
    const response = await sourceApi.get('/tickets?per_page=20');
    const tickets = response.data;
    
    console.log(`📊 Analyzing ${tickets.length} tickets:`);
    
    // Check date ranges
    const dates = tickets.map(t => new Date(t.created_at));
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));
    
    console.log(`📅 Date range: ${minDate.toISOString().split('T')[0]} to ${maxDate.toISOString().split('T')[0]}`);
    
    // Check ticket types
    const types = new Set();
    const customFields = new Set();
    
    tickets.forEach(ticket => {
      if (ticket.type) types.add(ticket.type);
      if (ticket.custom_fields) {
        Object.keys(ticket.custom_fields).forEach(key => customFields.add(key));
      }
    });
    
    console.log(`🏷️ Ticket types found: ${Array.from(types).join(', ') || 'None'}`);
    console.log(`🔧 Custom fields found: ${Array.from(customFields).join(', ') || 'None'}`);
    
    // Check for target ticket types
    const targetTypes = ['CAH', 'PWC-SAKS Global', 'IPG', 'Inspire Brands', 'Costa Forms', 'Robertshaw', 'Albert Heijn', 'HACH', 'Uline', 'Accuride'];
    let matchingTickets = 0;
    
    tickets.forEach(ticket => {
      const ticketType = ticket.type || ticket.subject || '';
      const hasTargetType = targetTypes.some(target => 
        ticketType.toLowerCase().includes(target.toLowerCase()) ||
        target.toLowerCase().includes(ticketType.toLowerCase())
      );
      if (hasTargetType) matchingTickets++;
    });
    
    console.log(`🎯 Tickets matching target types: ${matchingTickets}/${tickets.length}`);
    
  } catch (error) {
    console.error('❌ Test 4 Failed:', error.response?.status, error.response?.statusText);
  }
}

testFreshdeskAPI().catch(console.error);
