const axios = require('axios');

async function testFreshdeskAPI() {
  console.log('🔍 Testing Freshdesk API Parameters...\n');
  
  const sourceApi = axios.create({
    baseURL: 'https://pandohelp.freshdesk.com/api/v2',
    auth: {
      username: 'KcYLr6Q1V4zoDtnl1sKs',
      password: 'X'
    },
    timeout: 10000
  });

  // Test 1: Basic tickets endpoint
  try {
    console.log('📡 Test 1: Basic tickets endpoint...');
    const response = await sourceApi.get('/tickets?per_page=5');
    console.log(`✅ Success: ${response.status} - Found ${response.data.length} tickets`);
    
    if (response.data.length > 0) {
      const ticket = response.data[0];
      console.log(`📋 Sample ticket:`, {
        id: ticket.id,
        subject: ticket.subject,
        created_at: ticket.created_at,
        type: ticket.type,
        custom_fields: ticket.custom_fields
      });
    }
  } catch (error) {
    console.error('❌ Test 1 Failed:', error.response?.status, error.response?.statusText);
  }

  // Test 2: With updated_since parameter
  try {
    console.log('\n📡 Test 2: With updated_since parameter...');
    const response = await sourceApi.get('/tickets', {
      params: {
        per_page: 5,
        updated_since: '2024-01-01T00:00:00Z'
      }
    });
    console.log(`✅ Success: ${response.status} - Found ${response.data.length} tickets`);
  } catch (error) {
    console.error('❌ Test 2 Failed:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.error('Error details:', error.response.data);
    }
  }

  // Test 3: Simple pagination
  try {
    console.log('\n📡 Test 3: Simple pagination...');
    const response = await sourceApi.get('/tickets', {
      params: {
        page: 1,
        per_page: 10
      }
    });
    console.log(`✅ Success: ${response.status} - Found ${response.data.length} tickets`);
  } catch (error) {
    console.error('❌ Test 3 Failed:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.error('Error details:', error.response.data);
    }
  }

  // Test 4: Check ticket structure for filtering
  try {
    console.log('\n📡 Test 4: Analyzing ticket structure...');
    const response = await sourceApi.get('/tickets?per_page=20');
    const tickets = response.data;
    
    console.log(`📊 Analyzing ${tickets.length} tickets:`);
    
    // Check date ranges
    const dates = tickets.map(t => new Date(t.created_at));
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));
    
    console.log(`📅 Date range: ${minDate.toISOString().split('T')[0]} to ${maxDate.toISOString().split('T')[0]}`);
    
    // Check ticket types
    const types = new Set();
    const customFields = new Set();
    
    tickets.forEach(ticket => {
      if (ticket.type) types.add(ticket.type);
      if (ticket.custom_fields) {
        Object.keys(ticket.custom_fields).forEach(key => customFields.add(key));
      }
    });
    
    console.log(`🏷️ Ticket types found: ${Array.from(types).join(', ') || 'None'}`);
    console.log(`🔧 Custom fields found: ${Array.from(customFields).join(', ') || 'None'}`);
    
    // Check for target ticket types
    const targetTypes = ['CAH', 'PWC-SAKS Global', 'IPG', 'Inspire Brands', 'Costa Forms', 'Robertshaw', 'Albert Heijn', 'HACH', 'Uline', 'Accuride'];
    let matchingTickets = 0;
    
    tickets.forEach(ticket => {
      const ticketType = ticket.type || ticket.subject || '';
      const hasTargetType = targetTypes.some(target => 
        ticketType.toLowerCase().includes(target.toLowerCase()) ||
        target.toLowerCase().includes(ticketType.toLowerCase())
      );
      if (hasTargetType) matchingTickets++;
    });
    
    console.log(`🎯 Tickets matching target types: ${matchingTickets}/${tickets.length}`);

  } catch (error) {
    console.error('❌ Test 4 Failed:', error.response?.status, error.response?.statusText);
  }

  // Test 5: Test Freshdesk Search/Filter API
  try {
    console.log('\n📡 Test 5: Testing Freshdesk Search API...');

    // Test the search endpoint with date filters
    const searchParams = {
      query: '"created_at:>2020-01-01"'
    };

    const searchResponse = await sourceApi.get('/search/tickets', { params: searchParams });
    console.log(`✅ Search API Success: ${searchResponse.status} - Found ${searchResponse.data.results?.length || 0} tickets`);

  } catch (error) {
    console.error('❌ Test 5 Failed:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.error('Search API Error details:', error.response.data);
    }
  }

  // Test 6: Test with specific date range filters
  try {
    console.log('\n📡 Test 6: Testing date range filters...');

    // Try different date filter formats
    const dateTests = [
      { name: 'created_at gte/lte', params: { 'created_at[gte]': '2024-01-01', 'created_at[lte]': '2024-12-31', per_page: 5 } },
      { name: 'updated_since', params: { updated_since: '2024-01-01T00:00:00Z', per_page: 5 } },
      { name: 'created_since', params: { created_since: '2024-01-01T00:00:00Z', per_page: 5 } }
    ];

    for (const test of dateTests) {
      try {
        const response = await sourceApi.get('/tickets', { params: test.params });
        console.log(`  ✅ ${test.name}: ${response.status} - Found ${response.data.length} tickets`);
      } catch (err) {
        console.log(`  ❌ ${test.name}: ${err.response?.status} ${err.response?.statusText}`);
      }
    }

  } catch (error) {
    console.error('❌ Test 6 Failed:', error.message);
  }

  // Test 7: Test the exact filter URL pattern you provided
  try {
    console.log('\n📡 Test 7: Testing filter pattern from your URL...');

    // Based on your URL: https://pandohelp.freshdesk.com/a/tickets/filters/search?orderBy=created_at&orderType=desc&q[]=created%3Fis_greater_than...
    // Let's try to replicate this as an API call

    const filterParams = {
      orderBy: 'created_at',
      orderType: 'desc',
      per_page: 10
    };

    const filterResponse = await sourceApi.get('/tickets', { params: filterParams });
    console.log(`✅ Filter Pattern Success: ${filterResponse.status} - Found ${filterResponse.data.length} tickets`);

    // Check if any tickets have the target types
    const ticketsWithTypes = filterResponse.data.filter(ticket => {
      const targetTypes = ['CAH', 'PWC-SAKS Global', 'IPG', 'Inspire Brands', 'Costa Forms', 'Robertshaw', 'Albert Heijn', 'HACH', 'Uline', 'Accuride'];
      const ticketType = ticket.type || ticket.subject || JSON.stringify(ticket.custom_fields || {});
      return targetTypes.some(type => ticketType.toLowerCase().includes(type.toLowerCase()));
    });

    console.log(`🎯 Tickets with target types: ${ticketsWithTypes.length}/${filterResponse.data.length}`);

    if (ticketsWithTypes.length > 0) {
      console.log('📋 Sample matching ticket:', {
        id: ticketsWithTypes[0].id,
        subject: ticketsWithTypes[0].subject,
        type: ticketsWithTypes[0].type,
        created_at: ticketsWithTypes[0].created_at
      });
    }

  } catch (error) {
    console.error('❌ Test 7 Failed:', error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.error('Filter Error details:', error.response.data);
    }
  }
}

testFreshdeskAPI().catch(console.error);
