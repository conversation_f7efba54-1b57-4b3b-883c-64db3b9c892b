{"ast": null, "code": "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "map": {"version": 3, "names": ["getNodeName", "isTableElement", "element", "indexOf"], "sources": ["C:/tictetmigration/frontend/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js"], "sourcesContent": ["import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,eAAe,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC9C,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAACC,OAAO,CAACH,WAAW,CAACE,OAAO,CAAC,CAAC,IAAI,CAAC;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}