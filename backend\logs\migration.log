{"level":"error","message":"Error fetching tickets for 2025-05: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle","service":"ticket-migration","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle\n    at JSON.stringify (<anonymous>)\n    at Printf.template (C:\\tictetmigration\\backend\\utils\\logger.js:18:28)\n    at Printf.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\printf.js:11:26)\n    at Format.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\combine.js:20:24)\n    at Console._write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\modern.js:91:33)\n    at doWrite (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)\n    at writeOrBuffer (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:381:5)\n    at Writable.write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:302:11)\n    at DerivedLogger.ondata (C:\\tictetmigration\\backend\\node_modules\\winston\\node_modules\\readable-stream\\lib\\_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:530:35)","timestamp":"2025-08-28 12:34:38"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"auth":{"password":"X","username":"KcYLr6Q1V4zoDtnl1sKs"},"baseURL":"https://pandohelp.freshdesk.com/api/v2","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"cf_ticket_type":"CAH,PWC-SAKS Global,IPG,Inspire Brands,Costa Forms,Robertshaw,Albert Heijn,HACH,Uline,Accuride","created_at[gte]":"2025-05-31","created_at[lte]":"2025-06-30","order_by":"created_at","order_type":"asc","page":1,"per_page":100},"timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/tickets","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Error fetching page 1 for 2025-06: Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: pandohelp.freshdesk.com\r\nAuthorization: Basic ********************************\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"auth":"KcYLr6Q1V4zoDtnl1sKs:X","beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"pandohelp.freshdesk.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","pathname":"/api/v2/tickets","port":"","protocol":"https:","search":"?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"pandohelp.freshdesk.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Thu, 28 Aug 2025 07:04:43 GMT","Content-Type","application/json; charset=utf-8","Transfer-Encoding","chunked","Connection","keep-alive","CF-RAY","9761f4c8f9fa7e90-MAA","status","400 Bad Request","pragma","no-cache","x-request-id","4ca98a9d-7c14-9896-b829-a4aecd7e0009","x-freshdesk-api-version","latest=v2; requested=v2","content-security-policy","default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","Cache-Control","no-cache, no-store","x-xss-protection","1; mode=block","x-content-type-options","nosniff","expires","Wed, 13 Oct 2010 00:00:00 UTC","Set-Cookie","_x_w=31_1; path=/; HttpOnly; secure","Set-Cookie","__cf_bm=Dw08IvF_JyLoNCmRSsp4Dx6EkfFT1CtDxylAYAam034-1756364683-1.0.1.1-2701vekflbtsQBUYFa1cRZ2HGLzfoFnoIDdB7e3tLU1Nn_ru2fQKX.lXQnaRpdV85l.BZhYJgjZWkbYUOnCSW2EYfEqkXmf4rKHFLv9829Y; path=/; expires=Thu, 28-Aug-25 07:34:43 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","x-envoy-upstream-service-time","48","x-trace-id","00-282a6a103365b376940e4b10e6fc17d4-6ff20cd00816e13c-01","nel","{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","report-to","{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","x-fw-ratelimiting-managed","true","x-ratelimit-total","5000","x-ratelimit-remaining","4557","x-ratelimit-used-currentrequest","1","cf-cache-status","DYNAMIC","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","Server","cloudflare"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"auth":{"password":"X","username":"KcYLr6Q1V4zoDtnl1sKs"},"baseURL":"https://pandohelp.freshdesk.com/api/v2","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"cf_ticket_type":"CAH,PWC-SAKS Global,IPG,Inspire Brands,Costa Forms,Robertshaw,Albert Heijn,HACH,Uline,Accuride","created_at[gte]":"2025-05-31","created_at[lte]":"2025-06-30","order_by":"created_at","order_type":"asc","page":1,"per_page":100},"timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/tickets","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"description":"Validation failed","errors":[{"code":"invalid_field","field":"created_at","message":"Unexpected/invalid field in request"},{"code":"invalid_field","field":"cf_ticket_type","message":"Unexpected/invalid field in request"}]},"headers":{"cache-control":"no-cache, no-store","cf-cache-status":"DYNAMIC","cf-ray":"9761f4c8f9fa7e90-MAA","connection":"keep-alive","content-security-policy":"default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","content-type":"application/json; charset=utf-8","date":"Thu, 28 Aug 2025 07:04:43 GMT","expires":"Wed, 13 Oct 2010 00:00:00 UTC","nel":"{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","pragma":"no-cache","report-to":"{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","server":"cloudflare","set-cookie":"_x_w=31_1; path=/; HttpOnly; secure, __cf_bm=Dw08IvF_JyLoNCmRSsp4Dx6EkfFT1CtDxylAYAam034-1756364683-1.0.1.1-2701vekflbtsQBUYFa1cRZ2HGLzfoFnoIDdB7e3tLU1Nn_ru2fQKX.lXQnaRpdV85l.BZhYJgjZWkbYUOnCSW2EYfEqkXmf4rKHFLv9829Y; path=/; expires=Thu, 28-Aug-25 07:34:43 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","status":"400 Bad Request","strict-transport-security":"max-age=31536000; includeSubDomains; preload","transfer-encoding":"chunked","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"48","x-freshdesk-api-version":"latest=v2; requested=v2","x-fw-ratelimiting-managed":"true","x-ratelimit-remaining":"4557","x-ratelimit-total":"5000","x-ratelimit-used-currentrequest":"1","x-request-id":"4ca98a9d-7c14-9896-b829-a4aecd7e0009","x-trace-id":"00-282a6a103365b376940e4b10e6fc17d4-6ff20cd00816e13c-01","x-xss-protection":"1; mode=block"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: pandohelp.freshdesk.com\r\nAuthorization: Basic ********************************\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"auth":"KcYLr6Q1V4zoDtnl1sKs:X","beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"pandohelp.freshdesk.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","pathname":"/api/v2/tickets","port":"","protocol":"https:","search":"?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"pandohelp.freshdesk.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Thu, 28 Aug 2025 07:04:43 GMT","Content-Type","application/json; charset=utf-8","Transfer-Encoding","chunked","Connection","keep-alive","CF-RAY","9761f4c8f9fa7e90-MAA","status","400 Bad Request","pragma","no-cache","x-request-id","4ca98a9d-7c14-9896-b829-a4aecd7e0009","x-freshdesk-api-version","latest=v2; requested=v2","content-security-policy","default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","Cache-Control","no-cache, no-store","x-xss-protection","1; mode=block","x-content-type-options","nosniff","expires","Wed, 13 Oct 2010 00:00:00 UTC","Set-Cookie","_x_w=31_1; path=/; HttpOnly; secure","Set-Cookie","__cf_bm=Dw08IvF_JyLoNCmRSsp4Dx6EkfFT1CtDxylAYAam034-1756364683-1.0.1.1-2701vekflbtsQBUYFa1cRZ2HGLzfoFnoIDdB7e3tLU1Nn_ru2fQKX.lXQnaRpdV85l.BZhYJgjZWkbYUOnCSW2EYfEqkXmf4rKHFLv9829Y; path=/; expires=Thu, 28-Aug-25 07:34:43 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","x-envoy-upstream-service-time","48","x-trace-id","00-282a6a103365b376940e4b10e6fc17d4-6ff20cd00816e13c-01","nel","{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","report-to","{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","x-fw-ratelimiting-managed","true","x-ratelimit-total","5000","x-ratelimit-remaining","4557","x-ratelimit-used-currentrequest","1","cf-cache-status","DYNAMIC","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","Server","cloudflare"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-05-31&created_at[lte]=2025-06-30&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"service":"ticket-migration","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MigrationService.fetchTicketsForMonth (C:\\tictetmigration\\backend\\services\\migrationService.js:274:26)\n    at async MigrationService.fetchAllTickets (C:\\tictetmigration\\backend\\services\\migrationService.js:186:30)\n    at async MigrationService.start (C:\\tictetmigration\\backend\\services\\migrationService.js:62:23)","status":400,"timestamp":"2025-08-28 12:34:39"}
{"level":"error","message":"Error fetching tickets for 2025-06: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle","service":"ticket-migration","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle\n    at JSON.stringify (<anonymous>)\n    at Printf.template (C:\\tictetmigration\\backend\\utils\\logger.js:18:28)\n    at Printf.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\printf.js:11:26)\n    at Format.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\combine.js:20:24)\n    at Console._write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\modern.js:91:33)\n    at doWrite (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)\n    at writeOrBuffer (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:381:5)\n    at Writable.write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:302:11)\n    at DerivedLogger.ondata (C:\\tictetmigration\\backend\\node_modules\\winston\\node_modules\\readable-stream\\lib\\_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:530:35)","timestamp":"2025-08-28 12:34:39"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"auth":{"password":"X","username":"KcYLr6Q1V4zoDtnl1sKs"},"baseURL":"https://pandohelp.freshdesk.com/api/v2","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"cf_ticket_type":"CAH,PWC-SAKS Global,IPG,Inspire Brands,Costa Forms,Robertshaw,Albert Heijn,HACH,Uline,Accuride","created_at[gte]":"2025-06-30","created_at[lte]":"2025-07-31","order_by":"created_at","order_type":"asc","page":1,"per_page":100},"timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/tickets","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Error fetching page 1 for 2025-07: Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: pandohelp.freshdesk.com\r\nAuthorization: Basic ********************************\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"auth":"KcYLr6Q1V4zoDtnl1sKs:X","beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"pandohelp.freshdesk.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","pathname":"/api/v2/tickets","port":"","protocol":"https:","search":"?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"pandohelp.freshdesk.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Thu, 28 Aug 2025 07:04:44 GMT","Content-Type","application/json; charset=utf-8","Transfer-Encoding","chunked","Connection","keep-alive","CF-RAY","9761f4caec597e90-MAA","status","400 Bad Request","pragma","no-cache","x-request-id","eb8aa164-5807-9cad-811a-2964a646e2de","x-freshdesk-api-version","latest=v2; requested=v2","content-security-policy","default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","Cache-Control","no-cache, no-store","x-xss-protection","1; mode=block","x-content-type-options","nosniff","expires","Wed, 13 Oct 2010 00:00:00 UTC","Set-Cookie","_x_w=31_1; path=/; HttpOnly; secure","Set-Cookie","__cf_bm=LEyyUilUAIvbTo4XZDEeqiGSfvJy41qMivGXmBJYo0k-1756364684-1.0.1.1-nbGOVgSwoCM6MtJigteB.6IfxezV6jjH6DzthjY7fSmWMJjb36YH6ig6mnMDRMYjkp3V470QgaxcIypql5.v6J3ze1P4quCLQ_uWy5F1nU0; path=/; expires=Thu, 28-Aug-25 07:34:44 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","x-envoy-upstream-service-time","47","x-trace-id","00-105867fbc7e34c0c1de86e571415ff70-af1d0dad2c6608d3-01","nel","{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","report-to","{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","x-fw-ratelimiting-managed","true","x-ratelimit-total","5000","x-ratelimit-remaining","4556","x-ratelimit-used-currentrequest","1","cf-cache-status","DYNAMIC","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","Server","cloudflare"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"auth":{"password":"X","username":"KcYLr6Q1V4zoDtnl1sKs"},"baseURL":"https://pandohelp.freshdesk.com/api/v2","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"cf_ticket_type":"CAH,PWC-SAKS Global,IPG,Inspire Brands,Costa Forms,Robertshaw,Albert Heijn,HACH,Uline,Accuride","created_at[gte]":"2025-06-30","created_at[lte]":"2025-07-31","order_by":"created_at","order_type":"asc","page":1,"per_page":100},"timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/tickets","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"description":"Validation failed","errors":[{"code":"invalid_field","field":"created_at","message":"Unexpected/invalid field in request"},{"code":"invalid_field","field":"cf_ticket_type","message":"Unexpected/invalid field in request"}]},"headers":{"cache-control":"no-cache, no-store","cf-cache-status":"DYNAMIC","cf-ray":"9761f4caec597e90-MAA","connection":"keep-alive","content-security-policy":"default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","content-type":"application/json; charset=utf-8","date":"Thu, 28 Aug 2025 07:04:44 GMT","expires":"Wed, 13 Oct 2010 00:00:00 UTC","nel":"{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","pragma":"no-cache","report-to":"{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","server":"cloudflare","set-cookie":"_x_w=31_1; path=/; HttpOnly; secure, __cf_bm=LEyyUilUAIvbTo4XZDEeqiGSfvJy41qMivGXmBJYo0k-1756364684-1.0.1.1-nbGOVgSwoCM6MtJigteB.6IfxezV6jjH6DzthjY7fSmWMJjb36YH6ig6mnMDRMYjkp3V470QgaxcIypql5.v6J3ze1P4quCLQ_uWy5F1nU0; path=/; expires=Thu, 28-Aug-25 07:34:44 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","status":"400 Bad Request","strict-transport-security":"max-age=31536000; includeSubDomains; preload","transfer-encoding":"chunked","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"47","x-freshdesk-api-version":"latest=v2; requested=v2","x-fw-ratelimiting-managed":"true","x-ratelimit-remaining":"4556","x-ratelimit-total":"5000","x-ratelimit-used-currentrequest":"1","x-request-id":"eb8aa164-5807-9cad-811a-2964a646e2de","x-trace-id":"00-105867fbc7e34c0c1de86e571415ff70-af1d0dad2c6608d3-01","x-xss-protection":"1; mode=block"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: pandohelp.freshdesk.com\r\nAuthorization: Basic ********************************\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"auth":"KcYLr6Q1V4zoDtnl1sKs:X","beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"pandohelp.freshdesk.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","pathname":"/api/v2/tickets","port":"","protocol":"https:","search":"?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"pandohelp.freshdesk.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Thu, 28 Aug 2025 07:04:44 GMT","Content-Type","application/json; charset=utf-8","Transfer-Encoding","chunked","Connection","keep-alive","CF-RAY","9761f4caec597e90-MAA","status","400 Bad Request","pragma","no-cache","x-request-id","eb8aa164-5807-9cad-811a-2964a646e2de","x-freshdesk-api-version","latest=v2; requested=v2","content-security-policy","default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","Cache-Control","no-cache, no-store","x-xss-protection","1; mode=block","x-content-type-options","nosniff","expires","Wed, 13 Oct 2010 00:00:00 UTC","Set-Cookie","_x_w=31_1; path=/; HttpOnly; secure","Set-Cookie","__cf_bm=LEyyUilUAIvbTo4XZDEeqiGSfvJy41qMivGXmBJYo0k-1756364684-1.0.1.1-nbGOVgSwoCM6MtJigteB.6IfxezV6jjH6DzthjY7fSmWMJjb36YH6ig6mnMDRMYjkp3V470QgaxcIypql5.v6J3ze1P4quCLQ_uWy5F1nU0; path=/; expires=Thu, 28-Aug-25 07:34:44 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","x-envoy-upstream-service-time","47","x-trace-id","00-105867fbc7e34c0c1de86e571415ff70-af1d0dad2c6608d3-01","nel","{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","report-to","{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","x-fw-ratelimiting-managed","true","x-ratelimit-total","5000","x-ratelimit-remaining","4556","x-ratelimit-used-currentrequest","1","cf-cache-status","DYNAMIC","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","Server","cloudflare"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-06-30&created_at[lte]=2025-07-31&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"service":"ticket-migration","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MigrationService.fetchTicketsForMonth (C:\\tictetmigration\\backend\\services\\migrationService.js:274:26)\n    at async MigrationService.fetchAllTickets (C:\\tictetmigration\\backend\\services\\migrationService.js:186:30)\n    at async MigrationService.start (C:\\tictetmigration\\backend\\services\\migrationService.js:62:23)","status":400,"timestamp":"2025-08-28 12:34:39"}
{"level":"error","message":"Error fetching tickets for 2025-07: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle","service":"ticket-migration","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle\n    at JSON.stringify (<anonymous>)\n    at Printf.template (C:\\tictetmigration\\backend\\utils\\logger.js:18:28)\n    at Printf.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\printf.js:11:26)\n    at Format.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\combine.js:20:24)\n    at Console._write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\modern.js:91:33)\n    at doWrite (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)\n    at writeOrBuffer (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:381:5)\n    at Writable.write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:302:11)\n    at DerivedLogger.ondata (C:\\tictetmigration\\backend\\node_modules\\winston\\node_modules\\readable-stream\\lib\\_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:530:35)","timestamp":"2025-08-28 12:34:39"}
{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"auth":{"password":"X","username":"KcYLr6Q1V4zoDtnl1sKs"},"baseURL":"https://pandohelp.freshdesk.com/api/v2","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"cf_ticket_type":"CAH,PWC-SAKS Global,IPG,Inspire Brands,Costa Forms,Robertshaw,Albert Heijn,HACH,Uline,Accuride","created_at[gte]":"2025-07-31","created_at[lte]":"2025-08-28","order_by":"created_at","order_type":"asc","page":1,"per_page":100},"timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/tickets","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"level":"error","message":"Error fetching page 1 for 2025-08: Request failed with status code 400","name":"AxiosError","request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: pandohelp.freshdesk.com\r\nAuthorization: Basic ********************************\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"auth":"KcYLr6Q1V4zoDtnl1sKs:X","beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"pandohelp.freshdesk.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","pathname":"/api/v2/tickets","port":"","protocol":"https:","search":"?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"pandohelp.freshdesk.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Thu, 28 Aug 2025 07:04:44 GMT","Content-Type","application/json; charset=utf-8","Transfer-Encoding","chunked","Connection","keep-alive","CF-RAY","9761f4ccceb37e90-MAA","status","400 Bad Request","pragma","no-cache","x-request-id","41b0af59-520f-943f-8742-306b14177c3d","x-freshdesk-api-version","latest=v2; requested=v2","content-security-policy","default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","Cache-Control","no-cache, no-store","x-xss-protection","1; mode=block","x-content-type-options","nosniff","expires","Wed, 13 Oct 2010 00:00:00 UTC","Set-Cookie","_x_w=31_1; path=/; HttpOnly; secure","Set-Cookie","__cf_bm=1e.PKM2SgeNq9bQni3OrXJJQEpAfIzbv2PW54zmHiQU-1756364684-1.0.1.1-BwIS9DtMeqTRAO_6fnPqXsURrfljCxnpTTYlPtR1tpryodIX..Go5sqSQlwz.MzuGiw_nEi3OcGL2xPfaRZopVnLOiN0GjWW1x.tdnrzVsU; path=/; expires=Thu, 28-Aug-25 07:34:44 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","x-envoy-upstream-service-time","49","x-trace-id","00-09dd348896c632a8404c1db5fb401e93-7bf3961b6a877cb8-01","nel","{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","report-to","{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","x-fw-ratelimiting-managed","true","x-ratelimit-total","5000","x-ratelimit-remaining","4555","x-ratelimit-used-currentrequest","1","cf-cache-status","DYNAMIC","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","Server","cloudflare"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"response":{"config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"auth":{"password":"X","username":"KcYLr6Q1V4zoDtnl1sKs"},"baseURL":"https://pandohelp.freshdesk.com/api/v2","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","params":{"cf_ticket_type":"CAH,PWC-SAKS Global,IPG,Inspire Brands,Costa Forms,Robertshaw,Albert Heijn,HACH,Uline,Accuride","created_at[gte]":"2025-07-31","created_at[lte]":"2025-08-28","order_by":"created_at","order_type":"asc","page":1,"per_page":100},"timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/tickets","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"data":{"description":"Validation failed","errors":[{"code":"invalid_field","field":"created_at","message":"Unexpected/invalid field in request"},{"code":"invalid_field","field":"cf_ticket_type","message":"Unexpected/invalid field in request"}]},"headers":{"cache-control":"no-cache, no-store","cf-cache-status":"DYNAMIC","cf-ray":"9761f4ccceb37e90-MAA","connection":"keep-alive","content-security-policy":"default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","content-type":"application/json; charset=utf-8","date":"Thu, 28 Aug 2025 07:04:44 GMT","expires":"Wed, 13 Oct 2010 00:00:00 UTC","nel":"{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","pragma":"no-cache","report-to":"{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","server":"cloudflare","set-cookie":"_x_w=31_1; path=/; HttpOnly; secure, __cf_bm=1e.PKM2SgeNq9bQni3OrXJJQEpAfIzbv2PW54zmHiQU-1756364684-1.0.1.1-BwIS9DtMeqTRAO_6fnPqXsURrfljCxnpTTYlPtR1tpryodIX..Go5sqSQlwz.MzuGiw_nEi3OcGL2xPfaRZopVnLOiN0GjWW1x.tdnrzVsU; path=/; expires=Thu, 28-Aug-25 07:34:44 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","status":"400 Bad Request","strict-transport-security":"max-age=31536000; includeSubDomains; preload","transfer-encoding":"chunked","x-content-type-options":"nosniff","x-envoy-upstream-service-time":"49","x-freshdesk-api-version":"latest=v2; requested=v2","x-fw-ratelimiting-managed":"true","x-ratelimit-remaining":"4555","x-ratelimit-total":"5000","x-ratelimit-used-currentrequest":"1","x-request-id":"41b0af59-520f-943f-8742-306b14177c3d","x-trace-id":"00-09dd348896c632a8404c1db5fb401e93-7bf3961b6a877cb8-01","x-xss-protection":"1; mode=block"},"request":{"_closed":true,"_contentLength":0,"_defaultKeepAlive":true,"_ended":true,"_events":{},"_eventsCount":7,"_hasBody":true,"_header":"GET /api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride HTTP/1.1\r\nAccept: application/json, text/plain, */*\r\nUser-Agent: axios/1.11.0\r\nAccept-Encoding: gzip, compress, deflate, br\r\nHost: pandohelp.freshdesk.com\r\nAuthorization: Basic ********************************\r\nConnection: keep-alive\r\n\r\n","_headerSent":true,"_keepAliveTimeout":0,"_last":true,"_redirectable":{"_currentRequest":"[Circular]","_currentUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","_ended":true,"_ending":true,"_events":{"socket":[null,null]},"_eventsCount":3,"_options":{"agents":{},"auth":"KcYLr6Q1V4zoDtnl1sKs:X","beforeRedirects":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","User-Agent":"axios/1.11.0"},"hostname":"pandohelp.freshdesk.com","maxBodyLength":null,"maxRedirects":21,"method":"GET","nativeProtocols":{"http:":{"METHODS":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE"],"STATUS_CODES":{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I'm a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"},"globalAgent":{"_events":{},"_eventsCount":2,"defaultPort":80,"freeSockets":{},"keepAlive":true,"keepAliveMsecs":1000,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"http:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":0},"maxHeaderSize":16384},"https:":{"globalAgent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1}}},"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","pathname":"/api/v2/tickets","port":"","protocol":"https:","search":"?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride"},"_redirectCount":0,"_redirects":[],"_requestBodyBuffers":[],"_requestBodyLength":0,"_timeout":null,"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0}},"_removedConnection":false,"_removedContLen":false,"_removedTE":false,"_trailer":"","aborted":false,"agent":{"_events":{},"_eventsCount":2,"_sessionCache":{"list":["kambaa.freshdesk.com:443:::::::::::::::::::::","kambaa1726.freshdesk.com:443:::::::::::::::::::::","pandohelp.freshdesk.com:443:::::::::::::::::::::"],"map":{"kambaa.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,44,2,1,1,2,2,3,4,4,2,19,2,4,32,162,253,35,84,13,151,207,121,47,190,33,49,133,174,33,122,120,184,154,103,114,134,138,113,135,35,87,145,151,91,238,207,4,48,6,82,216,141,8,239,178,48,202,18,178,154,1,36,62,119,216,226,132,131,115,26,90,76,248,87,5,47,168,93,248,240,85,198,218,171,31,122,126,114,215,9,218,47,10,108,35,25,161,6,2,4,104,175,254,156,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,22,4,20,107,97,109,98,97,97,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,197,92,27,102,168,57,21,138,36,223,189,75,118,108,208,217,175,60,43,142,57,153,65,188,53,151,246,1,236,42,188,166,165,1,254,189,173,123,220,71,166,191,96,107,137,99,55,114,201,207,79,51,133,186,104,118,168,198,34,67,172,137,92,212,91,171,39,112,186,25,223,102,149,163,25,4,99,198,44,29,0,83,110,12,46,106,175,208,222,7,55,136,17,154,53,43,245,109,6,161,193,77,176,127,2,80,250,220,96,54,92,78,202,68,57,83,167,169,67,2,52,155,138,36,238,206,153,115,48,114,8,95,1,163,119,43,165,216,43,54,79,224,69,159,159,17,169,201,184,160,87,32,72,41,141,124,189,59,212,16,221,132,36,28,235,139,29,18,83,177,128,125,104,227,252,201,13,6,238,183,87,229,97,79,64,19,92,89,193,197,53,224,174,7,2,5,0,178,174,65,53,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"kambaa1726.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,47,2,1,1,2,2,3,4,4,2,19,2,4,32,152,139,250,186,122,234,229,194,18,16,60,37,229,31,28,51,167,93,15,96,30,216,236,66,246,17,215,92,43,245,47,150,4,48,225,146,234,196,195,133,102,100,125,85,180,59,24,169,244,159,246,252,197,88,45,103,7,12,129,172,169,192,163,232,233,154,176,254,248,253,70,41,60,169,29,165,1,203,61,123,105,83,161,6,2,4,104,175,254,125,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,26,4,24,107,97,109,98,97,97,49,55,50,54,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,129,192,199,249,225,241,41,64,209,44,232,192,127,161,172,24,79,97,145,243,136,178,140,164,191,80,195,122,131,198,234,226,128,81,81,149,102,34,195,28,142,178,22,46,6,119,232,72,46,173,209,168,183,209,89,193,54,143,251,249,108,177,215,246,250,255,199,100,200,154,186,64,215,127,189,35,220,190,152,30,183,175,145,45,255,135,205,142,181,168,36,105,112,138,76,115,93,206,231,4,167,33,80,217,97,34,129,48,85,77,216,229,157,75,218,166,50,232,110,25,205,59,91,218,144,69,176,27,237,23,159,98,110,54,29,43,136,208,135,249,153,76,41,31,56,241,221,112,247,51,216,189,149,202,189,63,100,83,106,9,122,232,218,135,18,213,79,89,241,132,172,142,5,149,25,179,3,230,209,5,243,64,17,14,53,255,85,175,218,238,160,207,174,6,2,4,85,84,36,196,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"},"pandohelp.freshdesk.com:443:::::::::::::::::::::":{"data":[48,130,5,46,2,1,1,2,2,3,4,4,2,19,2,4,32,163,231,151,91,70,206,208,227,9,177,90,76,189,107,58,221,86,130,235,86,10,222,200,10,139,10,69,252,239,250,227,243,4,48,135,139,83,144,69,88,2,72,229,196,225,83,107,159,134,28,247,123,191,50,120,253,231,76,248,54,48,71,210,18,92,232,130,8,13,83,49,157,204,65,19,45,71,139,140,118,250,112,161,6,2,4,104,175,255,114,162,4,2,2,28,32,163,130,3,174,48,130,3,170,48,130,3,80,160,3,2,1,2,2,17,0,135,211,62,107,212,21,208,10,14,103,101,59,133,11,206,44,48,10,6,8,42,134,72,206,61,4,3,2,48,59,49,11,48,9,6,3,85,4,6,19,2,85,83,49,30,48,28,6,3,85,4,10,19,21,71,111,111,103,108,101,32,84,114,117,115,116,32,83,101,114,118,105,99,101,115,49,12,48,10,6,3,85,4,3,19,3,87,69,49,48,30,23,13,50,53,48,55,48,53,49,53,48,52,49,54,90,23,13,50,53,49,48,48,51,49,54,48,52,49,51,90,48,24,49,22,48,20,6,3,85,4,3,19,13,102,114,101,115,104,100,101,115,107,46,99,111,109,48,89,48,19,6,7,42,134,72,206,61,2,1,6,8,42,134,72,206,61,3,1,7,3,66,0,4,103,102,132,103,245,13,240,144,155,145,149,13,166,215,117,198,73,232,180,244,101,151,47,48,162,45,201,100,239,15,109,1,43,116,45,197,159,169,191,34,46,185,95,149,232,27,122,197,207,161,122,163,100,55,209,118,32,224,205,199,48,83,202,94,163,130,2,86,48,130,2,82,48,14,6,3,85,29,15,1,1,255,4,4,3,2,7,128,48,19,6,3,85,29,37,4,12,48,10,6,8,43,6,1,5,5,7,3,1,48,12,6,3,85,29,19,1,1,255,4,2,48,0,48,29,6,3,85,29,14,4,22,4,20,247,19,144,43,4,218,104,58,23,216,18,191,39,86,55,197,200,129,185,121,48,31,6,3,85,29,35,4,24,48,22,128,20,144,119,146,53,103,196,255,168,204,169,230,123,217,128,121,123,204,147,249,56,48,94,6,8,43,6,1,5,5,7,1,1,4,82,48,80,48,39,6,8,43,6,1,5,5,7,48,1,134,27,104,116,116,112,58,47,47,111,46,112,107,105,46,103,111,111,103,47,115,47,119,101,49,47,104,57,77,48,37,6,8,43,6,1,5,5,7,48,2,134,25,104,116,116,112,58,47,47,105,46,112,107,105,46,103,111,111,103,47,119,101,49,46,99,114,116,48,41,6,3,85,29,17,4,34,48,32,130,13,102,114,101,115,104,100,101,115,107,46,99,111,109,130,15,42,46,102,114,101,115,104,100,101,115,107,46,99,111,109,48,19,6,3,85,29,32,4,12,48,10,48,8,6,6,103,129,12,1,2,1,48,54,6,3,85,29,31,4,47,48,45,48,43,160,41,160,39,134,37,104,116,116,112,58,47,47,99,46,112,107,105,46,103,111,111,103,47,119,101,49,47,104,121,86,79,121,89,75,120,122,57,69,46,99,114,108,48,130,1,3,6,10,43,6,1,4,1,214,121,2,4,2,4,129,244,4,129,241,0,239,0,118,0,221,220,202,52,149,215,225,22,5,231,149,50,250,199,159,248,61,28,80,223,219,0,58,20,18,118,10,44,172,187,200,42,0,0,1,151,219,84,204,91,0,0,4,3,0,71,48,69,2,33,0,206,136,38,20,154,209,171,156,233,142,41,198,54,212,171,102,158,168,174,20,112,1,163,123,41,172,132,62,96,212,39,182,2,32,31,98,148,156,253,171,132,86,239,7,180,37,38,132,0,150,105,194,212,212,212,99,21,53,103,146,93,56,71,143,90,253,0,117,0,204,251,15,106,133,113,9,101,254,149,155,83,206,233,178,124,34,233,133,92,13,151,141,182,169,126,84,192,254,76,13,176,0,0,1,151,219,84,204,104,0,0,4,3,0,70,48,68,2,32,114,55,34,73,180,71,52,202,164,221,0,122,203,184,177,214,236,52,164,30,112,118,213,184,83,161,230,245,40,218,250,20,2,32,51,235,193,195,66,141,10,146,18,100,106,223,142,105,89,250,153,95,137,207,207,62,15,48,210,158,198,30,234,109,18,22,48,10,6,8,42,134,72,206,61,4,3,2,3,72,0,48,69,2,32,35,95,115,37,163,44,42,211,118,134,247,8,142,132,81,119,61,114,68,114,77,75,0,66,48,24,93,185,245,15,236,105,2,33,0,170,192,10,102,206,143,157,228,26,67,21,49,135,98,186,201,74,123,178,180,149,34,188,163,23,177,148,20,48,217,17,225,164,2,4,0,166,25,4,23,112,97,110,100,111,104,101,108,112,46,102,114,101,115,104,100,101,115,107,46,99,111,109,169,5,2,3,0,253,32,170,129,211,4,129,208,87,70,189,249,222,37,2,9,168,16,179,1,50,107,215,72,251,154,238,105,171,252,121,146,134,187,242,166,223,210,141,85,13,53,106,7,141,53,41,209,61,99,123,98,47,95,75,93,184,74,141,207,82,108,71,34,0,187,148,39,254,96,36,9,218,61,28,96,8,205,45,250,201,42,14,161,6,157,7,199,209,93,53,255,1,122,129,48,207,114,2,50,198,55,215,94,180,142,165,65,247,189,166,126,72,252,142,77,41,15,108,69,250,163,31,177,40,176,99,73,70,162,90,199,139,209,102,85,91,211,114,24,116,234,179,89,91,209,6,92,119,28,74,234,240,111,238,210,165,93,172,73,141,107,159,201,210,111,8,129,25,97,133,22,5,48,66,144,43,245,71,205,207,141,214,194,47,59,192,156,49,26,218,28,201,42,5,92,117,158,151,205,0,200,51,236,46,160,176,185,47,237,193,143,220,44,201,87,174,6,2,4,50,159,251,116,175,4,2,2,56,0,179,3,2,1,29],"type":"Buffer"}}},"defaultPort":443,"freeSockets":{"pandohelp.freshdesk.com:443:::::::::::::::::::::":[{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000}]},"keepAlive":true,"keepAliveMsecs":1000,"maxCachedSessions":100,"maxFreeSockets":256,"maxSockets":null,"maxTotalSockets":null,"options":{"keepAlive":true,"noDelay":true,"path":null,"scheduling":"lifo","timeout":5000},"protocol":"https:","requests":{},"scheduling":"lifo","sockets":{},"totalSocketCount":1},"chunkedEncoding":false,"destroyed":true,"finished":true,"host":"pandohelp.freshdesk.com","maxHeadersCount":null,"maxRequestsOnConnectionReached":false,"method":"GET","outputData":[],"outputSize":0,"parser":null,"path":"/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","protocol":"https:","res":{"_consuming":true,"_dumped":false,"_events":{"end":[null,null]},"_eventsCount":4,"_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"aborted":false,"client":{"_SNICallback":null,"_closeAfterHandlingError":false,"_controlReleased":true,"_events":{"close":[null,null,null],"timeout":[null,null]},"_eventsCount":9,"_hadError":false,"_host":"pandohelp.freshdesk.com","_httpMessage":null,"_newSessionPending":false,"_parent":null,"_pendingData":null,"_pendingEncoding":"","_readableState":{"awaitDrainWriters":null,"buffer":[],"bufferIndex":0,"highWaterMark":16384,"length":0,"pipes":[]},"_rejectUnauthorized":true,"_requestCert":true,"_secureEstablished":true,"_securePending":false,"_server":null,"_sockname":null,"_tlsOptions":{"isServer":false,"pipe":false,"rejectUnauthorized":true,"requestCert":true,"secureContext":{"context":{}}},"_writableState":{"bufferedIndex":0,"corked":0,"highWaterMark":16384,"length":0,"pendingcb":0,"writelen":0},"allowHalfOpen":false,"alpnProtocol":false,"authorizationError":null,"authorized":true,"autoSelectFamilyAttemptedAddresses":["************:443"],"connecting":false,"encrypted":true,"parser":null,"secureConnecting":false,"servername":"pandohelp.freshdesk.com","ssl":{"_parent":{"onconnection":null,"reading":true},"_parentWrap":null,"_secureContext":{"context":{}},"reading":true},"timeout":5000},"complete":true,"httpVersion":"1.1","httpVersionMajor":1,"httpVersionMinor":1,"method":null,"rawHeaders":["Date","Thu, 28 Aug 2025 07:04:44 GMT","Content-Type","application/json; charset=utf-8","Transfer-Encoding","chunked","Connection","keep-alive","CF-RAY","9761f4ccceb37e90-MAA","status","400 Bad Request","pragma","no-cache","x-request-id","41b0af59-520f-943f-8742-306b14177c3d","x-freshdesk-api-version","latest=v2; requested=v2","content-security-policy","default-src 'self'; connect-src 'self' *.freshconnect.io/ *.freshsales.io/ *.freshworks.com/ *.freshdesk.com/ *.freshworksapi.com/ *.freshdeskusercontent.com/ *.freshdeskusercontent-euc.com/ *.freshdeskusercontent-in.com/ *.freshdeskusercontent-aus.com/ *.fconstage.io/ analytics.inlinemanual.com/__profile analytics.inlinemanual.com/__ptm backend.getbeamer.com/ heapanalytics.com/ d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/assets/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2lz1e868xzctj.cloudfront.net/ rum.haystack.es/freshdesk/analytics fonts.googleapis.com/ fonts.gstatic.com/ sentry.io/api/ wss://*.freshworksapi.com/ wss://*.freshdesk.com/ ********************.litix.io/ distillery.wistia.com/ pipedream.wistia.com/ freshworks.asknice.ly/ embedwistia-a.akamaihd.net/ embed-fastly.wistia.com/ maps.googleapis.com/ graph.microsoft.com/v1.0/ freshcaller-attachments.s3.amazonaws.com/production/ euc-freshcaller-attachments.s3.eu-central-1.amazonaws.com/production/ mec-freshcaller-attachments.s3.me-central-1.amazonaws.com/production/ au-freshcaller-attachments.s3-ap-southeast-2.amazonaws.com/production/ in-freshcaller-attachments.s3.ap-south-1.amazonaws.com/production/ pubsub.rtschannel.com/ api.fdcollab.com/ wss://pubsub.rtschannel.com/ cloudflareinsights.com/ data: blob: api.appcues.net/ wss://api.appcues.net/ fast.appcues.com/ cdn.jsdelivr.net/npm/@freshworks/crayons-icon@next/dist/ translate.googleapis.com/translate_a/t translate.googleapis.com/element/log fast.wistia.net/ fast.wistia.com/ embed-cloudfront.wistia.com/deliveries/ app.inlinemanual.com/ client-api.auryc.com/ *.surveyserv.com *.freshsurvey.com *.freddybot.com; font-src 'self' *.freshdesk.com/ fonts.gstatic.com/ fonts.googleapis.com/ cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/ fast.wistia.net/ fast.wistia.com/ *.freddybot.com cdn.inlinemanual.com/inm/author/ data:; frame-src 'self' https:; img-src 'self' https: data: blob:; media-src 'self' https: blob:; object-src 'none'; script-src 'self' *.freshworksapi.com/ *.freshworks.com/ *.myfreshworks.com/ *.freshdesk.com/ *.freshchat.com/ *.freshcaller.com/ *.freshconnect.io/ *.freshcloud.io/ *.fconstage.io/ accounts.freshworks.com/ wchat.freshchat.com/js/ wchat.freshchat.com/widget/js/ assets.calendly.com/assets/external/widget.js d3h0owdjgzys62.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ app.getbeamer.com/js/beamer-embed.js analytics.inlinemanual.com/ cdn.inlinemanual.com/embed/ cdn.heapanalytics.com/ s3.amazonaws.com/assets.freshdesk.com/ cdnjs.cloudflare.com/ ajax.cloudflare.com/ static.cloudflareinsights.com/ js.chargebee.com/v1/chargebee.js js.braintreegateway.com/v1/braintree.js static.freshdev.io/ fast.wistia.net/ fast.wistia.com/ static.getbeamer.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js js-agent.newrelic.com/ www.googletagmanager.com/gtag/js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.js www.dropbox.com/static/api/2/dropins.js js.live.net/v7.2/OneDrive.js apis.google.com/ asknice.ly bam.nr-data.net/ www.google-analytics.com/analytics.js maps.googleapis.com/ unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.esm.js unpkg.com/@freshworks/crayons@v3/dist/crayons/crayons.js s3.amazonaws.com/freshcaller-widget-loader/ in-freshcaller-widget-loader.s3.ap-south-1.amazonaws.com/ s3.eu-central-1.amazonaws.com/euc-freshcaller-widget-loader/ mec-freshcaller-widget-loader.s3.me-central-1.amazonaws.com/ au-freshcaller-widget-loader.s3-ap-southeast-2.amazonaws.com/ www.dropbox.com/static/api/1/dropbox.js fast.appcues.com/ translate.google.com/translate_a/element.js translate.googleapis.com/_/translate_http/_/js/ translate-pa.googleapis.com/v1/supportedLanguages cdn.surveyserv.com/widget.min.js cdn.freshdev.io/assets/marketplace-heap.js cdn.freshcloud.io/assets/marketplace-heap.js cdn.inlinemanual.com/inm/author/ app.inlinemanual.com/ *.surveyserv.com *.freshsurvey.com www.google.com/recaptcha/ www.gstatic.com/recaptcha/ *.freddybot.com d3el5jsqgryo0a.cloudfront.net accounts.google.com/ 'unsafe-inline' 'unsafe-eval'; style-src 'self' *.freshworks.com *.myfreshworks.com/ *.freshchat.com/ d3h0owdjgzys62.cloudfront.net/ dcdu85ocrj5q6.cloudfront.net/ dtdafz6i4gvv1.cloudfront.net/ d3r4aewxkdubw4.cloudfront.net/ d2uy6ubiilaqku.cloudfront.net/ fonts.googleapis.com/ app.getbeamer.com/styles/beamer-embed.css s3.amazonaws.com/assets.freshdesk.com/ *.freshdesk.com/ wchat.freshchat.com/ calendly.com/ unpkg.com/@webcomponents/webcomponentsjs@2.4.3/custom-elements-es5-adapter.js unpkg.com/@webcomponents/webcomponentsjs@2.4.3/webcomponents-loader.js static.asknice.ly/dist/standalone/asknicely-in-app-conversation.css fast.appcues.com/ asknice.ly *.surveyserv.com *.freshsurvey.com *.freddybot.com cdn.inlinemanual.com/inm/author/ 'unsafe-inline'; worker-src 'self' blob:","Cache-Control","no-cache, no-store","x-xss-protection","1; mode=block","x-content-type-options","nosniff","expires","Wed, 13 Oct 2010 00:00:00 UTC","Set-Cookie","_x_w=31_1; path=/; HttpOnly; secure","Set-Cookie","__cf_bm=1e.PKM2SgeNq9bQni3OrXJJQEpAfIzbv2PW54zmHiQU-1756364684-1.0.1.1-BwIS9DtMeqTRAO_6fnPqXsURrfljCxnpTTYlPtR1tpryodIX..Go5sqSQlwz.MzuGiw_nEi3OcGL2xPfaRZopVnLOiN0GjWW1x.tdnrzVsU; path=/; expires=Thu, 28-Aug-25 07:34:44 GMT; domain=.freshdesk.com; HttpOnly; Secure; SameSite=None","x-envoy-upstream-service-time","49","x-trace-id","00-09dd348896c632a8404c1db5fb401e93-7bf3961b6a877cb8-01","nel","{ \"report_to\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true}","report-to","{ \"group\": \"nel-endpoint-freshdesk\", \"max_age\": 2592000, \"include_subdomains\": true, \"endpoints\": [{\"url\": \"https://edge-admin.us-east-1.freshedge.net/nelreports/freshdesk\"}]}","x-fw-ratelimiting-managed","true","x-ratelimit-total","5000","x-ratelimit-remaining","4555","x-ratelimit-used-currentrequest","1","cf-cache-status","DYNAMIC","Strict-Transport-Security","max-age=31536000; includeSubDomains; preload","Server","cloudflare"],"rawTrailers":[],"redirects":[],"req":"[Circular]","responseUrl":"https://KcYLr6Q1V4zoDtnl1sKs:<EMAIL>/api/v2/tickets?page=1&per_page=100&created_at[gte]=2025-07-31&created_at[lte]=2025-08-28&order_by=created_at&order_type=asc&cf_ticket_type=CAH,PWC-SAKS+Global,IPG,Inspire+Brands,Costa+Forms,Robertshaw,Albert+Heijn,HACH,Uline,Accuride","socket":null,"statusCode":400,"statusMessage":"Bad Request","upgrade":false,"url":""},"reusedSocket":true,"sendDate":false,"shouldKeepAlive":true,"strictContentLength":false,"timeoutCb":null,"upgradeOrConnect":false,"useChunkedEncodingByDefault":false,"writable":true},"status":400,"statusText":"Bad Request"},"service":"ticket-migration","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:2090:12)\n    at IncomingMessage.handleStreamEnd (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:3207:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (C:\\tictetmigration\\backend\\node_modules\\axios\\dist\\node\\axios.cjs:4317:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MigrationService.fetchTicketsForMonth (C:\\tictetmigration\\backend\\services\\migrationService.js:274:26)\n    at async MigrationService.fetchAllTickets (C:\\tictetmigration\\backend\\services\\migrationService.js:186:30)\n    at async MigrationService.start (C:\\tictetmigration\\backend\\services\\migrationService.js:62:23)","status":400,"timestamp":"2025-08-28 12:34:39"}
{"level":"error","message":"Error fetching tickets for 2025-08: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle","service":"ticket-migration","stack":"TypeError: Converting circular structure to JSON\n    --> starting at object with constructor 'ClientRequest'\n    |     property 'res' -> object with constructor 'IncomingMessage'\n    --- property 'req' closes the circle\n    at JSON.stringify (<anonymous>)\n    at Printf.template (C:\\tictetmigration\\backend\\utils\\logger.js:18:28)\n    at Printf.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\printf.js:11:26)\n    at Format.transform (C:\\tictetmigration\\backend\\node_modules\\logform\\combine.js:20:24)\n    at Console._write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\modern.js:91:33)\n    at doWrite (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:390:139)\n    at writeOrBuffer (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:381:5)\n    at Writable.write (C:\\tictetmigration\\backend\\node_modules\\winston-transport\\node_modules\\readable-stream\\lib\\_stream_writable.js:302:11)\n    at DerivedLogger.ondata (C:\\tictetmigration\\backend\\node_modules\\winston\\node_modules\\readable-stream\\lib\\_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:530:35)","timestamp":"2025-08-28 12:34:39"}
{"level":"info","message":"Total tickets fetched: 0","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f","service":"ticket-migration","timestamp":"2025-08-28 12:34:39"}
{"level":"info","message":"Pre-migrating contacts and companies","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f","service":"ticket-migration","timestamp":"2025-08-28 12:34:39"}
{"failed":0,"level":"info","message":"Migration completed","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f","processed":0,"service":"ticket-migration","successful":0,"timestamp":"2025-08-28 12:34:39","total":0}
{"level":"info","message":"Client left migration room","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f","service":"ticket-migration","socketId":"hIe1hO2uVsW8j6INAAAF","timestamp":"2025-08-28 12:34:39"}
{"level":"info","message":"Client joined migration room","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f","service":"ticket-migration","socketId":"hIe1hO2uVsW8j6INAAAF","timestamp":"2025-08-28 12:34:39"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"ticket-migration","timestamp":"2025-08-28 12:36:19"}
{"timestamp":"2025-08-28 12:38:33","level":"info","message":"Socket.IO initialized","service":"ticket-migration"}
{"timestamp":"2025-08-28 12:38:33","level":"info","message":"Connected to SQLite database","service":"ticket-migration"}
{"timestamp":"2025-08-28 12:38:33","level":"info","message":"Database tables created successfully","service":"ticket-migration"}
{"timestamp":"2025-08-28 12:38:33","level":"info","message":"Database initialized successfully","service":"ticket-migration"}
{"timestamp":"2025-08-28 12:38:33","level":"info","message":"Server running on port 5000","service":"ticket-migration"}
{"timestamp":"2025-08-28 12:38:33","level":"info","message":"Environment: undefined","service":"ticket-migration"}
{"timestamp":"2025-08-28 12:38:34","level":"info","message":"Client connected","service":"ticket-migration","socketId":"6und5KIt8gTTKDnvAAAB"}
{"timestamp":"2025-08-28 12:38:34","level":"info","message":"Client left migration room","service":"ticket-migration","socketId":"6und5KIt8gTTKDnvAAAB","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f"}
{"timestamp":"2025-08-28 12:38:34","level":"info","message":"Client joined migration room","service":"ticket-migration","socketId":"6und5KIt8gTTKDnvAAAB","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f"}
{"timestamp":"2025-08-28 12:38:34","level":"info","message":"Client left migration room","service":"ticket-migration","socketId":"6und5KIt8gTTKDnvAAAB","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f"}
{"timestamp":"2025-08-28 12:38:34","level":"info","message":"Client joined migration room","service":"ticket-migration","socketId":"6und5KIt8gTTKDnvAAAB","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f"}
{"timestamp":"2025-08-28 12:38:41","level":"info","message":"Client connected","service":"ticket-migration","socketId":"Rnpoel5M3eXZPForAAAE"}
{"timestamp":"2025-08-28 12:38:45","level":"info","message":"Client disconnected","service":"ticket-migration","socketId":"6und5KIt8gTTKDnvAAAB"}
{"timestamp":"2025-08-28 12:38:46","level":"info","message":"Client connected","service":"ticket-migration","socketId":"VWwSTAjsx4L0iKUdAAAH"}
{"timestamp":"2025-08-28 12:38:47","level":"info","message":"Client joined migration room","service":"ticket-migration","socketId":"VWwSTAjsx4L0iKUdAAAH","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f"}
{"timestamp":"2025-08-28 12:38:47","level":"info","message":"Client left migration room","service":"ticket-migration","socketId":"VWwSTAjsx4L0iKUdAAAH","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f"}
{"timestamp":"2025-08-28 12:38:47","level":"info","message":"Client joined migration room","service":"ticket-migration","socketId":"VWwSTAjsx4L0iKUdAAAH","migrationId":"a069bd37-35d6-4dde-afcf-85f6107c1f1f"}
{"timestamp":"2025-08-28 12:39:44","level":"info","message":"Migration started","service":"ticket-migration","migrationId":"2d9aa9fa-58f5-4c44-baf8-705448e14233","sourceUrl":"https://pandohelp.freshdesk.com/api/v2","targetUrl":"https://kambaa1726.freshdesk.com/api/v2"}
{"timestamp":"2025-08-28 12:39:44","level":"info","message":"Fetching tickets from source","service":"ticket-migration","migrationId":"2d9aa9fa-58f5-4c44-baf8-705448e14233"}
{"timestamp":"2025-08-28 12:39:44","level":"info","message":"Fetching tickets for 68 month ranges with 10 ticket types","service":"ticket-migration","migrationId":"2d9aa9fa-58f5-4c44-baf8-705448e14233"}
{"timestamp":"2025-08-28 12:39:44","level":"info","message":"Client joined migration room","service":"ticket-migration","socketId":"Rnpoel5M3eXZPForAAAE","migrationId":"2d9aa9fa-58f5-4c44-baf8-705448e14233"}
{"timestamp":"2025-08-28 12:39:45","level":"error","message":"Error fetching page 1 for 2020-01: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:45","level":"error","message":"Error fetching tickets for 2020-01: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:45","level":"error","message":"Error fetching page 1 for 2020-02: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:45","level":"error","message":"Error fetching tickets for 2020-02: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:45","level":"error","message":"Error fetching page 1 for 2020-03: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:45","level":"error","message":"Error fetching tickets for 2020-03: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:46","level":"error","message":"Error fetching page 1 for 2020-04: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:46","level":"error","message":"Error fetching tickets for 2020-04: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:46","level":"error","message":"Error fetching page 1 for 2020-05: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:46","level":"error","message":"Error fetching tickets for 2020-05: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:46","level":"error","message":"Error fetching page 1 for 2020-06: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:46","level":"error","message":"Error fetching tickets for 2020-06: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching page 1 for 2020-07: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching tickets for 2020-07: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching page 1 for 2020-08: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching tickets for 2020-08: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching page 1 for 2020-09: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching tickets for 2020-09: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching page 1 for 2020-10: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:47","level":"error","message":"Error fetching tickets for 2020-10: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:48","level":"error","message":"Error fetching page 1 for 2020-11: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:48","level":"error","message":"Error fetching tickets for 2020-11: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:48","level":"error","message":"Error fetching page 1 for 2020-12: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:48","level":"error","message":"Error fetching tickets for 2020-12: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching page 1 for 2021-01: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching tickets for 2021-01: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching page 1 for 2021-02: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching tickets for 2021-02: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching page 1 for 2021-03: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching tickets for 2021-03: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching page 1 for 2021-04: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:49","level":"error","message":"Error fetching tickets for 2021-04: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:50","level":"error","message":"Error fetching page 1 for 2021-05: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:50","level":"error","message":"Error fetching tickets for 2021-05: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:50","level":"error","message":"Error fetching page 1 for 2021-06: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:50","level":"error","message":"Error fetching tickets for 2021-06: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:50","level":"error","message":"Error fetching page 1 for 2021-07: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:50","level":"error","message":"Error fetching tickets for 2021-07: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:51","level":"error","message":"Error fetching page 1 for 2021-08: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:51","level":"error","message":"Error fetching tickets for 2021-08: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:51","level":"error","message":"Error fetching page 1 for 2021-09: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:51","level":"error","message":"Error fetching tickets for 2021-09: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:51","level":"error","message":"Error fetching page 1 for 2021-10: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:51","level":"error","message":"Error fetching tickets for 2021-10: Request failed with status code 400","service":"ticket-migration","status":400,"statusText":"Bad Request"}
{"timestamp":"2025-08-28 12:39:52","level":"info","message":"SIGINT received, shutting down gracefully","service":"ticket-migration"}
