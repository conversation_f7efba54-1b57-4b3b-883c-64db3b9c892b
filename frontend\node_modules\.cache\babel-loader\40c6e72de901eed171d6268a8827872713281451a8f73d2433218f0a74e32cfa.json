{"ast": null, "code": "import { ForwardRef, Memo } from 'react-is';\n\n// Simplified polyfill for IE11 support\n// https://github.com/JamesMGreene/Function.name/blob/58b314d4a983110c3682f1228f845d39ccca1817/Function.name.js#L3\nconst fnNameMatchRegex = /^\\s*function(?:\\s|\\s*\\/\\*.*\\*\\/\\s*)+([^(\\s/]*)\\s*/;\nexport function getFunctionName(fn) {\n  const match = `${fn}`.match(fnNameMatchRegex);\n  const name = match && match[1];\n  return name || '';\n}\nfunction getFunctionComponentName(Component, fallback = '') {\n  return Component.displayName || Component.name || getFunctionName(Component) || fallback;\n}\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  const functionName = getFunctionComponentName(innerType);\n  return outerType.displayName || (functionName !== '' ? `${wrapperName}(${functionName})` : wrapperName);\n}\n\n/**\n * cherry-pick from\n * https://github.com/facebook/react/blob/769b1f270e1251d9dbdce0fcbd9e92e502d059b8/packages/shared/getComponentName.js\n * originally forked from recompose/getDisplayName with added IE11 support\n */\nexport default function getDisplayName(Component) {\n  if (Component == null) {\n    return undefined;\n  }\n  if (typeof Component === 'string') {\n    return Component;\n  }\n  if (typeof Component === 'function') {\n    return getFunctionComponentName(Component, 'Component');\n  }\n\n  // TypeScript can't have components as objects but they exist in the form of `memo` or `Suspense`\n  if (typeof Component === 'object') {\n    switch (Component.$$typeof) {\n      case ForwardRef:\n        return getWrappedName(Component, Component.render, 'ForwardRef');\n      case Memo:\n        return getWrappedName(Component, Component.type, 'memo');\n      default:\n        return undefined;\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["ForwardRef", "Memo", "fnNameMatchRegex", "getFunctionName", "fn", "match", "name", "getFunctionComponentName", "Component", "fallback", "displayName", "getWrappedName", "outerType", "innerType", "wrapperName", "functionName", "getDisplayName", "undefined", "$$typeof", "render", "type"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/utils/esm/getDisplayName/getDisplayName.js"], "sourcesContent": ["import { ForwardRef, Memo } from 'react-is';\n\n// Simplified polyfill for IE11 support\n// https://github.com/JamesMGreene/Function.name/blob/58b314d4a983110c3682f1228f845d39ccca1817/Function.name.js#L3\nconst fnNameMatchRegex = /^\\s*function(?:\\s|\\s*\\/\\*.*\\*\\/\\s*)+([^(\\s/]*)\\s*/;\nexport function getFunctionName(fn) {\n  const match = `${fn}`.match(fnNameMatchRegex);\n  const name = match && match[1];\n  return name || '';\n}\nfunction getFunctionComponentName(Component, fallback = '') {\n  return Component.displayName || Component.name || getFunctionName(Component) || fallback;\n}\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  const functionName = getFunctionComponentName(innerType);\n  return outerType.displayName || (functionName !== '' ? `${wrapperName}(${functionName})` : wrapperName);\n}\n\n/**\n * cherry-pick from\n * https://github.com/facebook/react/blob/769b1f270e1251d9dbdce0fcbd9e92e502d059b8/packages/shared/getComponentName.js\n * originally forked from recompose/getDisplayName with added IE11 support\n */\nexport default function getDisplayName(Component) {\n  if (Component == null) {\n    return undefined;\n  }\n  if (typeof Component === 'string') {\n    return Component;\n  }\n  if (typeof Component === 'function') {\n    return getFunctionComponentName(Component, 'Component');\n  }\n\n  // TypeScript can't have components as objects but they exist in the form of `memo` or `Suspense`\n  if (typeof Component === 'object') {\n    switch (Component.$$typeof) {\n      case ForwardRef:\n        return getWrappedName(Component, Component.render, 'ForwardRef');\n      case Memo:\n        return getWrappedName(Component, Component.type, 'memo');\n      default:\n        return undefined;\n    }\n  }\n  return undefined;\n}"], "mappings": "AAAA,SAASA,UAAU,EAAEC,IAAI,QAAQ,UAAU;;AAE3C;AACA;AACA,MAAMC,gBAAgB,GAAG,mDAAmD;AAC5E,OAAO,SAASC,eAAeA,CAACC,EAAE,EAAE;EAClC,MAAMC,KAAK,GAAG,GAAGD,EAAE,EAAE,CAACC,KAAK,CAACH,gBAAgB,CAAC;EAC7C,MAAMI,IAAI,GAAGD,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;EAC9B,OAAOC,IAAI,IAAI,EAAE;AACnB;AACA,SAASC,wBAAwBA,CAACC,SAAS,EAAEC,QAAQ,GAAG,EAAE,EAAE;EAC1D,OAAOD,SAAS,CAACE,WAAW,IAAIF,SAAS,CAACF,IAAI,IAAIH,eAAe,CAACK,SAAS,CAAC,IAAIC,QAAQ;AAC1F;AACA,SAASE,cAAcA,CAACC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAE;EACzD,MAAMC,YAAY,GAAGR,wBAAwB,CAACM,SAAS,CAAC;EACxD,OAAOD,SAAS,CAACF,WAAW,KAAKK,YAAY,KAAK,EAAE,GAAG,GAAGD,WAAW,IAAIC,YAAY,GAAG,GAAGD,WAAW,CAAC;AACzG;;AAEA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,cAAcA,CAACR,SAAS,EAAE;EAChD,IAAIA,SAAS,IAAI,IAAI,EAAE;IACrB,OAAOS,SAAS;EAClB;EACA,IAAI,OAAOT,SAAS,KAAK,QAAQ,EAAE;IACjC,OAAOA,SAAS;EAClB;EACA,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;IACnC,OAAOD,wBAAwB,CAACC,SAAS,EAAE,WAAW,CAAC;EACzD;;EAEA;EACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,QAAQA,SAAS,CAACU,QAAQ;MACxB,KAAKlB,UAAU;QACb,OAAOW,cAAc,CAACH,SAAS,EAAEA,SAAS,CAACW,MAAM,EAAE,YAAY,CAAC;MAClE,KAAKlB,IAAI;QACP,OAAOU,cAAc,CAACH,SAAS,EAAEA,SAAS,CAACY,IAAI,EAAE,MAAM,CAAC;MAC1D;QACE,OAAOH,SAAS;IACpB;EACF;EACA,OAAOA,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}