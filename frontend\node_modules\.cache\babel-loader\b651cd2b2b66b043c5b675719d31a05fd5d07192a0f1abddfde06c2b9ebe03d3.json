{"ast": null, "code": "export { default } from './usePreviousProps';", "map": {"version": 3, "names": ["default"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/utils/esm/usePreviousProps/index.js"], "sourcesContent": ["export { default } from './usePreviousProps';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}