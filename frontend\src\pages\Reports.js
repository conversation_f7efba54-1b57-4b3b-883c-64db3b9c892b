import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Download as DownloadIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Schedule as PendingIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { toast } from 'react-toastify';

import { reportsApi, handleApiError } from '../services/api';

const Reports = () => {
  const { migrationId } = useParams();
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  const [report, setReport] = useState(null);
  const [allReports, setAllReports] = useState([]);
  const [overallStats, setOverallStats] = useState(null);

  useEffect(() => {
    if (migrationId) {
      loadMigrationReport();
    } else {
      loadAllReports();
    }
  }, [migrationId]);

  const loadMigrationReport = async () => {
    try {
      setLoading(true);
      const response = await reportsApi.getReport(migrationId);
      setReport(response.data);
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to load report: ${errorInfo.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadAllReports = async () => {
    try {
      setLoading(true);
      const response = await reportsApi.getReports();
      setAllReports(response.data.migrations);
      setOverallStats(response.data.overallStats);
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to load reports: ${errorInfo.message}`);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async () => {
    if (!migrationId) return;
    
    try {
      setExporting(true);
      const response = await reportsApi.exportReport(migrationId);
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `migration-${migrationId}-report.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success('Report exported successfully!');
    } catch (err) {
      const errorInfo = handleApiError(err);
      toast.error(`Failed to export report: ${errorInfo.message}`);
    } finally {
      setExporting(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': case 'success': return 'success';
      case 'running': case 'processing': return 'primary';
      case 'failed': return 'error';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': case 'success': return <SuccessIcon />;
      case 'failed': return <ErrorIcon />;
      case 'running': case 'processing': return <PendingIcon />;
      default: return <PendingIcon />;
    }
  };

  const formatDuration = (seconds) => {
    if (!seconds) return 'N/A';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (migrationId && report) {
    // Single Migration Report View
    return (
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4">
            Migration Report: {migrationId}
          </Typography>
          <Button
            variant="outlined"
            startIcon={exporting ? <CircularProgress size={16} /> : <DownloadIcon />}
            onClick={exportReport}
            disabled={exporting}
          >
            {exporting ? 'Exporting...' : 'Export CSV'}
          </Button>
        </Box>

        <Grid container spacing={3}>
          {/* Summary Statistics */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Migration Summary
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="textSecondary">
                      Status
                    </Typography>
                    <Chip
                      icon={getStatusIcon(report.migration.status)}
                      label={report.migration.status}
                      color={getStatusColor(report.migration.status)}
                    />
                  </Grid>
                  
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="textSecondary">
                      Total Tickets
                    </Typography>
                    <Typography variant="h6">
                      {report.stats.total}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="textSecondary">
                      Success Rate
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      {report.stats.successRate}%
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="textSecondary">
                      Duration
                    </Typography>
                    <Typography variant="h6">
                      {formatDuration(report.migration.duration)}
                    </Typography>
                  </Grid>
                </Grid>

                <Box mt={2}>
                  <Typography variant="body2" color="textSecondary">
                    Source: {report.migration.source_url}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Target: {report.migration.target_url}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Created: {format(new Date(report.migration.created_at), 'MMM dd, yyyy HH:mm:ss')}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Progress Breakdown */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Progress Breakdown
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Processed
                    </Typography>
                    <Typography variant="h5">
                      {report.stats.processed}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Successful
                    </Typography>
                    <Typography variant="h5" color="success.main">
                      {report.stats.successful}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Failed
                    </Typography>
                    <Typography variant="h5" color="error.main">
                      {report.stats.failed}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      Pending
                    </Typography>
                    <Typography variant="h5">
                      {report.stats.pending}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Failed Tickets */}
          {report.failedTickets && report.failedTickets.length > 0 && (
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6">
                    Failed Tickets ({report.failedTickets.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Ticket ID</TableCell>
                          <TableCell>Error Message</TableCell>
                          <TableCell>Retry Count</TableCell>
                          <TableCell>Failed At</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {report.failedTickets.map((ticket, index) => (
                          <TableRow key={index}>
                            <TableCell>{ticket.source_ticket_id}</TableCell>
                            <TableCell>
                              <Typography variant="body2" color="error">
                                {ticket.error_message}
                              </Typography>
                            </TableCell>
                            <TableCell>{ticket.retry_count}</TableCell>
                            <TableCell>
                              {format(new Date(ticket.created_at), 'MMM dd, HH:mm')}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  }

  // All Reports View
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Migration Reports
      </Typography>

      {overallStats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Migrations
                </Typography>
                <Typography variant="h4">
                  {overallStats.total_migrations || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Tickets
                </Typography>
                <Typography variant="h4">
                  {overallStats.total_tickets || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Success Rate
                </Typography>
                <Typography variant="h4" color="success.main">
                  {overallStats.successRate || 0}%
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Failed Tickets
                </Typography>
                <Typography variant="h4" color="error.main">
                  {overallStats.total_failed || 0}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            All Migrations
          </Typography>
          
          {allReports.length === 0 ? (
            <Alert severity="info">
              No migration reports found. Start a migration to see reports here.
            </Alert>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Migration ID</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Source</TableCell>
                    <TableCell>Target</TableCell>
                    <TableCell>Tickets</TableCell>
                    <TableCell>Success Rate</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {allReports.map((migration) => (
                    <TableRow key={migration.id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {migration.id.substring(0, 8)}...
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(migration.status)}
                          label={migration.status}
                          color={getStatusColor(migration.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new URL(migration.source_url).hostname}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new URL(migration.target_url).hostname}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {migration.successful_tickets || 0} / {migration.total_tickets || 0}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color={
                          (migration.total_tickets > 0 ? 
                            (migration.successful_tickets / migration.total_tickets * 100) : 0) >= 90 ? 
                            'success.main' : 'text.primary'
                        }>
                          {migration.total_tickets > 0 ? 
                            ((migration.successful_tickets / migration.total_tickets) * 100).toFixed(1) : 0}%
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {format(new Date(migration.created_at), 'MMM dd, yyyy')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          href={`/reports/${migration.id}`}
                        >
                          View Report
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Reports;
