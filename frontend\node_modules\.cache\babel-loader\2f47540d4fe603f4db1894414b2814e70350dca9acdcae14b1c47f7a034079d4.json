{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSpeedDialIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDialIcon', slot);\n}\nconst speedDialIconClasses = generateUtilityClasses('MuiSpeedDialIcon', ['root', 'icon', 'iconOpen', 'iconWithOpenIconOpen', 'openIcon', 'openIconOpen']);\nexport default speedDialIconClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSpeedDialIconUtilityClass", "slot", "speedDialIconClasses"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/SpeedDialIcon/speedDialIconClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSpeedDialIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSpeedDialIcon', slot);\n}\nconst speedDialIconClasses = generateUtilityClasses('MuiSpeedDialIcon', ['root', 'icon', 'iconOpen', 'iconWithOpenIconOpen', 'openIcon', 'openIconOpen']);\nexport default speedDialIconClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGJ,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AACzJ,eAAeI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}