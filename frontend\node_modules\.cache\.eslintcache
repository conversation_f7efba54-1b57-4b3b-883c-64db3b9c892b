[{"C:\\tictetmigration\\frontend\\src\\index.js": "1", "C:\\tictetmigration\\frontend\\src\\App.js": "2", "C:\\tictetmigration\\frontend\\src\\pages\\Dashboard.js": "3", "C:\\tictetmigration\\frontend\\src\\pages\\Migration.js": "4", "C:\\tictetmigration\\frontend\\src\\contexts\\SocketContext.js": "5", "C:\\tictetmigration\\frontend\\src\\pages\\Reports.js": "6", "C:\\tictetmigration\\frontend\\src\\pages\\Configuration.js": "7", "C:\\tictetmigration\\frontend\\src\\components\\Navigation.js": "8", "C:\\tictetmigration\\frontend\\src\\services\\api.js": "9"}, {"size": 1581, "mtime": 1756362573757, "results": "10", "hashOfConfig": "11"}, {"size": 1698, "mtime": 1756362583739, "results": "12", "hashOfConfig": "11"}, {"size": 9722, "mtime": 1756362659918, "results": "13", "hashOfConfig": "11"}, {"size": 18637, "mtime": 1756363515327, "results": "14", "hashOfConfig": "11"}, {"size": 3562, "mtime": 1756362599549, "results": "15", "hashOfConfig": "11"}, {"size": 18655, "mtime": 1756362945939, "results": "16", "hashOfConfig": "11"}, {"size": 16609, "mtime": 1756364258752, "results": "17", "hashOfConfig": "11"}, {"size": 1905, "mtime": 1756362611710, "results": "18", "hashOfConfig": "11"}, {"size": 3339, "mtime": 1756362627295, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1otngn7", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\tictetmigration\\frontend\\src\\index.js", [], [], "C:\\tictetmigration\\frontend\\src\\App.js", [], [], "C:\\tictetmigration\\frontend\\src\\pages\\Dashboard.js", ["47"], [], "C:\\tictetmigration\\frontend\\src\\pages\\Migration.js", ["48", "49", "50", "51", "52", "53", "54", "55"], [], "C:\\tictetmigration\\frontend\\src\\contexts\\SocketContext.js", [], [], "C:\\tictetmigration\\frontend\\src\\pages\\Reports.js", ["56"], [], "C:\\tictetmigration\\frontend\\src\\pages\\Configuration.js", ["57", "58"], [], "C:\\tictetmigration\\frontend\\src\\components\\Navigation.js", [], [], "C:\\tictetmigration\\frontend\\src\\services\\api.js", [], [], {"ruleId": "59", "severity": 1, "message": "60", "line": 93, "column": 9, "nodeType": "61", "messageId": "62", "endLine": 93, "endColumn": 23}, {"ruleId": "59", "severity": 1, "message": "63", "line": 15, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 15, "endColumn": 8}, {"ruleId": "59", "severity": 1, "message": "64", "line": 16, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 16, "endColumn": 12}, {"ruleId": "59", "severity": 1, "message": "65", "line": 17, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 17, "endColumn": 12}, {"ruleId": "59", "severity": 1, "message": "66", "line": 18, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 18, "endColumn": 17}, {"ruleId": "59", "severity": 1, "message": "67", "line": 19, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 19, "endColumn": 12}, {"ruleId": "59", "severity": 1, "message": "68", "line": 20, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 20, "endColumn": 11}, {"ruleId": "59", "severity": 1, "message": "69", "line": 21, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 21, "endColumn": 8}, {"ruleId": "70", "severity": 1, "message": "71", "line": 84, "column": 6, "nodeType": "72", "endLine": 84, "endColumn": 50, "suggestions": "73"}, {"ruleId": "70", "severity": 1, "message": "74", "line": 50, "column": 6, "nodeType": "72", "endLine": 50, "endColumn": 19, "suggestions": "75"}, {"ruleId": "59", "severity": 1, "message": "76", "line": 12, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 12, "endColumn": 10}, {"ruleId": "59", "severity": 1, "message": "77", "line": 26, "column": 3, "nodeType": "61", "messageId": "62", "endLine": 26, "endColumn": 15}, "no-unused-vars", "'formatDuration' is assigned a value but never used.", "Identifier", "unusedVar", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'Paper' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadMigrationStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["78"], "React Hook useEffect has a missing dependency: 'loadMigrationReport'. Either include it or remove the dependency array.", ["79"], "'Divider' is defined but never used.", "'ListItemIcon' is defined but never used.", {"desc": "80", "fix": "81"}, {"desc": "82", "fix": "83"}, "Update the dependencies array to be: [migrationId, joinMigration, leaveMigration, loadMigrationStatus]", {"range": "84", "text": "85"}, "Update the dependencies array to be: [loadMigrationReport, migrationId]", {"range": "86", "text": "87"}, [2031, 2075], "[migrationId, joinMigration, leaveMigration, loadMigrationStatus]", [1126, 1139], "[loadMigrationReport, migrationId]"]