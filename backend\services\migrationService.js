const axios = require('axios');
const logger = require('../utils/logger');
const { getDatabase } = require('../database/init');
const { 
  emitMigrationProgress, 
  emitMigrationStatus, 
  emitTicketUpdate, 
  emitError 
} = require('./socketService');

class MigrationService {
  constructor(config) {
    this.migrationId = config.migrationId;
    this.sourceUrl = config.sourceUrl;
    this.sourceApiKey = config.sourceApiKey;
    this.targetUrl = config.targetUrl;
    this.targetApiKey = config.targetApiKey;
    this.options = config.options || {};
    
    this.batchSize = this.options.batchSize || parseInt(process.env.BATCH_SIZE) || 50;
    this.maxRetries = this.options.maxRetries || parseInt(process.env.MAX_RETRIES) || 3;
    this.retryDelay = this.options.retryDelay || parseInt(process.env.RETRY_DELAY_MS) || 5000;
    this.rateLimit = this.options.rateLimit || parseInt(process.env.RATE_LIMIT_DELAY_MS) || 1000;
    
    this.isRunning = false;
    this.shouldStop = false;
    
    // Setup axios instances
    this.sourceApi = axios.create({
      baseURL: this.sourceUrl,
      auth: {
        username: this.sourceApiKey,
        password: 'X'
      },
      timeout: 30000
    });
    
    this.targetApi = axios.create({
      baseURL: this.targetUrl,
      auth: {
        username: this.targetApiKey,
        password: 'X'
      },
      timeout: 30000
    });
  }

  async start() {
    if (this.isRunning) {
      throw new Error('Migration is already running');
    }

    this.isRunning = true;
    this.shouldStop = false;

    try {
      await this.updateMigrationStatus('running', { started_at: new Date().toISOString() });
      emitMigrationStatus(this.migrationId, 'running');

      // Step 1: Fetch all tickets from source
      logger.info('Fetching tickets from source', { migrationId: this.migrationId });
      const tickets = await this.fetchAllTickets();
      
      await this.updateMigrationStatus('processing', { 
        total_tickets: tickets.length 
      });

      // Step 2: Process tickets in batches
      let processed = 0;
      let successful = 0;
      let failed = 0;

      for (let i = 0; i < tickets.length; i += this.batchSize) {
        if (this.shouldStop) {
          logger.info('Migration stopped by user', { migrationId: this.migrationId });
          break;
        }

        const batch = tickets.slice(i, i + this.batchSize);
        
        for (const ticket of batch) {
          if (this.shouldStop) break;

          try {
            await this.migrateTicket(ticket);
            successful++;
            emitTicketUpdate(this.migrationId, {
              ticketId: ticket.id,
              status: 'success'
            });
          } catch (error) {
            failed++;
            logger.logTicketMigration(this.migrationId, ticket.id, 'failed', error);
            emitTicketUpdate(this.migrationId, {
              ticketId: ticket.id,
              status: 'failed',
              error: error.message
            });
          }

          processed++;
          
          // Update progress
          await this.updateMigrationProgress(processed, successful, failed);
          emitMigrationProgress(this.migrationId, {
            processed,
            successful,
            failed,
            total: tickets.length,
            progress: (processed / tickets.length) * 100
          });

          // Rate limiting
          await this.delay(this.rateLimit);
        }
      }

      // Complete migration
      const finalStatus = this.shouldStop ? 'cancelled' : 'completed';
      await this.updateMigrationStatus(finalStatus, {
        completed_at: new Date().toISOString(),
        processed_tickets: processed,
        successful_tickets: successful,
        failed_tickets: failed
      });

      emitMigrationStatus(this.migrationId, finalStatus, {
        processed,
        successful,
        failed,
        total: tickets.length
      });

      logger.logMigrationComplete(this.migrationId, {
        total: tickets.length,
        processed,
        successful,
        failed
      });

    } catch (error) {
      logger.error('Migration failed:', error);
      await this.updateMigrationStatus('failed', {
        error_message: error.message,
        completed_at: new Date().toISOString()
      });
      emitError(this.migrationId, error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  async fetchAllTickets() {
    const tickets = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      try {
        const response = await this.sourceApi.get('/tickets', {
          params: {
            page,
            per_page: 100
          }
        });

        const pageTickets = response.data;
        tickets.push(...pageTickets);

        // Check if there are more pages
        hasMore = pageTickets.length === 100;
        page++;

        logger.info(`Fetched ${pageTickets.length} tickets from page ${page - 1}`, {
          migrationId: this.migrationId
        });

        // Rate limiting
        await this.delay(this.rateLimit);

      } catch (error) {
        logger.error('Error fetching tickets:', error);
        throw new Error(`Failed to fetch tickets: ${error.message}`);
      }
    }

    logger.info(`Total tickets fetched: ${tickets.length}`, {
      migrationId: this.migrationId
    });

    return tickets;
  }

  async migrateTicket(sourceTicket) {
    const db = getDatabase();
    
    // Insert ticket migration record
    const ticketMigrationId = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO ticket_migrations (migration_id, source_ticket_id, status)
         VALUES (?, ?, 'processing')`,
        [this.migrationId, sourceTicket.id],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });

    try {
      // Transform ticket data for target system
      const targetTicketData = this.transformTicketData(sourceTicket);
      
      // Create ticket in target system
      const targetTicketResponse = await this.targetApi.post('/tickets', targetTicketData);
      const targetTicket = targetTicketResponse.data;

      // Update ticket migration record
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE ticket_migrations 
           SET target_ticket_id = ?, status = 'success', migrated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [targetTicket.id, ticketMigrationId],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Migrate conversations and attachments
      await this.migrateConversations(sourceTicket.id, targetTicket.id, ticketMigrationId);

      logger.logTicketMigration(this.migrationId, sourceTicket.id, 'success');

    } catch (error) {
      // Update ticket migration record with error
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE ticket_migrations 
           SET status = 'failed', error_message = ?
           WHERE id = ?`,
          [error.message, ticketMigrationId],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      throw error;
    }
  }

  transformTicketData(sourceTicket) {
    // Transform source ticket data to target format
    // This is a basic transformation - customize based on your needs
    return {
      subject: sourceTicket.subject,
      description: sourceTicket.description || 'Migrated ticket',
      priority: sourceTicket.priority,
      status: sourceTicket.status,
      requester_id: sourceTicket.requester_id,
      type: sourceTicket.type,
      source: 1, // Email source
      tags: sourceTicket.tags || [],
      custom_fields: sourceTicket.custom_fields || {}
    };
  }

  async migrateConversations(sourceTicketId, targetTicketId, ticketMigrationId) {
    try {
      // Fetch conversations from source
      const conversationsResponse = await this.sourceApi.get(`/tickets/${sourceTicketId}/conversations`);
      const conversations = conversationsResponse.data;

      for (const conversation of conversations) {
        await this.migrateConversation(conversation, targetTicketId, ticketMigrationId);
        await this.delay(this.rateLimit / 2); // Shorter delay for conversations
      }

    } catch (error) {
      logger.error('Error migrating conversations:', error);
      // Don't throw - conversations are not critical
    }
  }

  async migrateConversation(sourceConversation, targetTicketId, ticketMigrationId) {
    const db = getDatabase();
    
    try {
      // Create conversation in target system
      const conversationData = {
        body: sourceConversation.body,
        private: sourceConversation.private || false
      };

      const targetConversationResponse = await this.targetApi.post(
        `/tickets/${targetTicketId}/notes`,
        conversationData
      );

      // Record conversation migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO conversation_migrations 
           (ticket_migration_id, source_conversation_id, target_conversation_id, status, migrated_at)
           VALUES (?, ?, ?, 'success', CURRENT_TIMESTAMP)`,
          [ticketMigrationId, sourceConversation.id, targetConversationResponse.data.id],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

    } catch (error) {
      logger.error('Error migrating conversation:', error);
      // Record failed conversation migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO conversation_migrations 
           (ticket_migration_id, source_conversation_id, status, error_message)
           VALUES (?, ?, 'failed', ?)`,
          [ticketMigrationId, sourceConversation.id, error.message],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }
  }

  async updateMigrationStatus(status, additionalData = {}) {
    const db = getDatabase();
    const fields = Object.keys(additionalData);
    const values = Object.values(additionalData);
    
    let sql = 'UPDATE migrations SET status = ?';
    const params = [status];
    
    if (fields.length > 0) {
      sql += ', ' + fields.map(field => `${field} = ?`).join(', ');
      params.push(...values);
    }
    
    sql += ' WHERE id = ?';
    params.push(this.migrationId);

    return new Promise((resolve, reject) => {
      db.run(sql, params, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async updateMigrationProgress(processed, successful, failed) {
    return this.updateMigrationStatus('running', {
      processed_tickets: processed,
      successful_tickets: successful,
      failed_tickets: failed
    });
  }

  stop() {
    this.shouldStop = true;
    logger.info('Migration stop requested', { migrationId: this.migrationId });
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = MigrationService;
