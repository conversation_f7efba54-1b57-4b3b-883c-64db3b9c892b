const axios = require('axios');
const logger = require('../utils/logger');
const { getDatabase } = require('../database/init');
const { 
  emitMigrationProgress, 
  emitMigrationStatus, 
  emitTicketUpdate, 
  emitError 
} = require('./socketService');

class MigrationService {
  constructor(config) {
    this.migrationId = config.migrationId;
    this.sourceUrl = config.sourceUrl;
    this.sourceApiKey = config.sourceApiKey;
    this.targetUrl = config.targetUrl;
    this.targetApiKey = config.targetApiKey;
    this.options = config.options || {};
    
    this.batchSize = this.options.batchSize || parseInt(process.env.BATCH_SIZE) || 50;
    this.maxRetries = this.options.maxRetries || parseInt(process.env.MAX_RETRIES) || 3;
    this.retryDelay = this.options.retryDelay || parseInt(process.env.RETRY_DELAY_MS) || 5000;
    this.rateLimit = this.options.rateLimit || parseInt(process.env.RATE_LIMIT_DELAY_MS) || 1000;
    
    this.isRunning = false;
    this.shouldStop = false;
    
    // Setup axios instances
    this.sourceApi = axios.create({
      baseURL: this.sourceUrl,
      auth: {
        username: this.sourceApiKey,
        password: 'X'
      },
      timeout: 30000
    });
    
    this.targetApi = axios.create({
      baseURL: this.targetUrl,
      auth: {
        username: this.targetApiKey,
        password: 'X'
      },
      timeout: 30000
    });
  }

  async start() {
    if (this.isRunning) {
      throw new Error('Migration is already running');
    }

    this.isRunning = true;
    this.shouldStop = false;

    try {
      await this.updateMigrationStatus('running', { started_at: new Date().toISOString() });
      emitMigrationStatus(this.migrationId, 'running');

      // Step 1: Fetch all tickets from source
      logger.info('Fetching tickets from source', { migrationId: this.migrationId });
      const tickets = await this.fetchAllTickets();

      await this.updateMigrationStatus('processing', {
        total_tickets: tickets.length
      });

      // Step 2: Pre-migrate contacts and companies
      logger.info('Pre-migrating contacts and companies', { migrationId: this.migrationId });
      await this.preMigrateContactsAndCompanies(tickets);

      // Step 3: Process tickets in batches
      let processed = 0;
      let successful = 0;
      let failed = 0;

      for (let i = 0; i < tickets.length; i += this.batchSize) {
        if (this.shouldStop) {
          logger.info('Migration stopped by user', { migrationId: this.migrationId });
          break;
        }

        const batch = tickets.slice(i, i + this.batchSize);
        
        for (const ticket of batch) {
          if (this.shouldStop) break;

          try {
            await this.migrateTicket(ticket);
            successful++;
            emitTicketUpdate(this.migrationId, {
              ticketId: ticket.id,
              status: 'success'
            });
          } catch (error) {
            failed++;
            logger.logTicketMigration(this.migrationId, ticket.id, 'failed', error);
            emitTicketUpdate(this.migrationId, {
              ticketId: ticket.id,
              status: 'failed',
              error: error.message
            });
          }

          processed++;
          
          // Update progress
          await this.updateMigrationProgress(processed, successful, failed);
          emitMigrationProgress(this.migrationId, {
            processed,
            successful,
            failed,
            total: tickets.length,
            progress: (processed / tickets.length) * 100
          });

          // Rate limiting
          await this.delay(this.rateLimit);
        }
      }

      // Complete migration
      const finalStatus = this.shouldStop ? 'cancelled' : 'completed';
      await this.updateMigrationStatus(finalStatus, {
        completed_at: new Date().toISOString(),
        processed_tickets: processed,
        successful_tickets: successful,
        failed_tickets: failed
      });

      emitMigrationStatus(this.migrationId, finalStatus, {
        processed,
        successful,
        failed,
        total: tickets.length
      });

      logger.logMigrationComplete(this.migrationId, {
        total: tickets.length,
        processed,
        successful,
        failed
      });

    } catch (error) {
      logger.error('Migration failed:', error);
      await this.updateMigrationStatus('failed', {
        error_message: error.message,
        completed_at: new Date().toISOString()
      });
      emitError(this.migrationId, error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  async fetchAllTickets() {
    const tickets = [];

    // Get ticket types from options or use defaults
    const ticketTypes = this.options.ticketTypes || [
      'CAH',
      'PWC-SAKS Global',
      'IPG',
      'Inspire Brands',
      'Costa Forms',
      'Robertshaw',
      'Albert Heijn',
      'HACH',
      'Uline',
      'Accuride'
    ];

    // Generate month ranges from configured dates or defaults
    const monthRanges = this.generateMonthRanges();

    logger.info(`Fetching tickets for ${monthRanges.length} month ranges with ${ticketTypes.length} ticket types`, {
      migrationId: this.migrationId
    });

    for (const monthRange of monthRanges) {
      if (this.shouldStop) break;

      try {
        const monthTickets = await this.fetchTicketsForMonth(monthRange, ticketTypes);
        tickets.push(...monthTickets);

        logger.info(`Fetched ${monthTickets.length} tickets for ${monthRange.month}`, {
          migrationId: this.migrationId,
          month: monthRange.month,
          totalSoFar: tickets.length
        });

        // Rate limiting between month requests
        await this.delay(this.rateLimit);

      } catch (error) {
        logger.error(`Error fetching tickets for ${monthRange.month}:`, {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText
        });
        // Continue with next month instead of failing completely
      }
    }

    logger.info(`Total tickets fetched: ${tickets.length}`, {
      migrationId: this.migrationId
    });

    return tickets;
  }

  generateMonthRanges() {
    const ranges = [];
    const startDate = new Date(this.options.dateRangeFrom || '2020-01-01');
    const endDate = new Date(this.options.dateRangeTo || new Date().toISOString().split('T')[0]);

    let currentMonth = new Date(startDate);

    while (currentMonth <= endDate) {
      const year = currentMonth.getFullYear();
      const month = currentMonth.getMonth();

      // First day of the month
      const fromDate = new Date(year, month, 1);

      // Last day of the month
      const toDate = new Date(year, month + 1, 0, 23, 59, 59, 999);

      // Don't go beyond configured end date
      if (toDate > endDate) {
        toDate.setTime(endDate.getTime());
      }

      ranges.push({
        month: `${year}-${String(month + 1).padStart(2, '0')}`,
        from: fromDate.toISOString(),
        to: toDate.toISOString(),
        fromFormatted: fromDate.toISOString().split('T')[0],
        toFormatted: toDate.toISOString().split('T')[0]
      });

      // Move to next month
      currentMonth.setMonth(currentMonth.getMonth() + 1);
    }

    return ranges;
  }

  async fetchTicketsForMonth(monthRange, ticketTypes) {
    const tickets = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      try {
        // Use simple pagination - we'll filter on the client side
        // This is more reliable than complex API filters
        const params = {
          page,
          per_page: 100
        };

        const response = await this.sourceApi.get('/tickets', { params });
        const pageTickets = response.data;

        // Filter tickets by date range and type
        const filteredTickets = this.filterTickets(pageTickets, monthRange, ticketTypes);
        tickets.push(...filteredTickets);

        // Check if there are more pages
        hasMore = pageTickets.length === 100;
        page++;

        logger.debug(`Fetched page ${page - 1} for ${monthRange.month}: ${pageTickets.length} tickets, ${filteredTickets.length} after filtering`, {
          migrationId: this.migrationId
        });

        // Rate limiting between page requests
        await this.delay(this.rateLimit / 2);

      } catch (error) {
        logger.error(`Error fetching page ${page} for ${monthRange.month}:`, {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText
        });

        // If it's a rate limit error, wait longer and retry
        if (error.response && error.response.status === 429) {
          logger.warn('Rate limit hit, waiting 60 seconds before retry');
          await this.delay(60000);
          continue; // Retry the same page
        }

        throw error;
      }
    }

    return tickets;
  }

  filterTickets(tickets, monthRange, ticketTypes) {
    return tickets.filter(ticket => {
      // Filter by date range
      const createdAt = new Date(ticket.created_at);
      const fromDate = new Date(monthRange.from);
      const toDate = new Date(monthRange.to);

      const isInDateRange = createdAt >= fromDate && createdAt <= toDate;
      if (!isInDateRange) return false;

      // Filter by ticket types if specified
      if (!ticketTypes || ticketTypes.length === 0) {
        return true; // No type filter, include all tickets in date range
      }

      // Check various possible fields where ticket type might be stored
      const ticketType = ticket.type ||
                        ticket.cf_ticket_type ||
                        ticket.custom_fields?.cf_ticket_type ||
                        ticket.custom_fields?.ticket_type ||
                        ticket.subject; // Sometimes type is in subject

      if (!ticketType) return false;

      // Check if ticket type matches any of the required types
      return ticketTypes.some(type => {
        if (typeof ticketType === 'string') {
          return ticketType.toLowerCase().includes(type.toLowerCase()) ||
                 type.toLowerCase().includes(ticketType.toLowerCase());
        }
        return false;
      });
    });
  }

  async migrateTicket(sourceTicket) {
    const db = getDatabase();
    
    // Insert ticket migration record
    const ticketMigrationId = await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO ticket_migrations (migration_id, source_ticket_id, status)
         VALUES (?, ?, 'processing')`,
        [this.migrationId, sourceTicket.id],
        function(err) {
          if (err) reject(err);
          else resolve(this.lastID);
        }
      );
    });

    try {
      // Transform ticket data for target system
      const targetTicketData = await this.transformTicketData(sourceTicket);

      // Create ticket in target system
      const targetTicketResponse = await this.targetApi.post('/tickets', targetTicketData);
      const targetTicket = targetTicketResponse.data;

      // Update ticket migration record
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE ticket_migrations 
           SET target_ticket_id = ?, status = 'success', migrated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [targetTicket.id, ticketMigrationId],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Migrate conversations and attachments
      await this.migrateConversations(sourceTicket.id, targetTicket.id, ticketMigrationId);

      logger.logTicketMigration(this.migrationId, sourceTicket.id, 'success');

    } catch (error) {
      // Update ticket migration record with error
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE ticket_migrations 
           SET status = 'failed', error_message = ?
           WHERE id = ?`,
          [error.message, ticketMigrationId],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      throw error;
    }
  }

  async transformTicketData(sourceTicket) {
    const db = getDatabase();

    // Get migrated contact ID
    let targetRequesterId = sourceTicket.requester_id;
    if (sourceTicket.requester_id) {
      const migratedContact = await new Promise((resolve, reject) => {
        db.get(
          'SELECT target_contact_id FROM contact_migrations WHERE source_contact_id = ? AND status = "success"',
          [sourceTicket.requester_id],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
      if (migratedContact) {
        targetRequesterId = migratedContact.target_contact_id;
      }
    }

    // Get migrated company ID
    let targetCompanyId = null;
    if (sourceTicket.company_id) {
      const migratedCompany = await new Promise((resolve, reject) => {
        db.get(
          'SELECT target_company_id FROM company_migrations WHERE source_company_id = ? AND status = "success"',
          [sourceTicket.company_id],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });
      if (migratedCompany) {
        targetCompanyId = migratedCompany.target_company_id;
      }
    }

    // Transform source ticket data to target format
    return {
      subject: sourceTicket.subject,
      description: sourceTicket.description || 'Migrated ticket',
      priority: sourceTicket.priority,
      status: sourceTicket.status,
      requester_id: targetRequesterId,
      company_id: targetCompanyId,
      type: sourceTicket.type,
      source: 1, // Email source
      tags: sourceTicket.tags || [],
      custom_fields: sourceTicket.custom_fields || {},
      // Preserve original ticket metadata
      cc_emails: sourceTicket.cc_emails || [],
      due_by: sourceTicket.due_by,
      fr_due_by: sourceTicket.fr_due_by,
      group_id: sourceTicket.group_id,
      product_id: sourceTicket.product_id
    };
  }

  async migrateConversations(sourceTicketId, targetTicketId, ticketMigrationId) {
    try {
      // Fetch conversations from source using Freshdesk API
      const conversationsResponse = await this.sourceApi.get(`/tickets/${sourceTicketId}/conversations`);
      const conversations = conversationsResponse.data;

      for (const conversation of conversations) {
        await this.migrateConversation(conversation, targetTicketId, ticketMigrationId);
        await this.delay(this.rateLimit / 2); // Shorter delay for conversations
      }

    } catch (error) {
      logger.error('Error migrating conversations:', error);
      // Don't throw - conversations are not critical
    }
  }

  async migrateConversation(sourceConversation, targetTicketId, ticketMigrationId) {
    const db = getDatabase();

    try {
      // Determine if this should be a reply or note based on conversation type
      const isPrivate = sourceConversation.private || false;
      const isFromAgent = sourceConversation.user_id && sourceConversation.user_id !== sourceConversation.ticket?.requester_id;

      let endpoint, conversationData;

      if (isPrivate || isFromAgent) {
        // Use notes endpoint for private conversations or agent responses
        endpoint = `/tickets/${targetTicketId}/notes`;
        conversationData = {
          body: sourceConversation.body,
          private: isPrivate,
          notify_emails: sourceConversation.notify_emails || []
        };
      } else {
        // Use reply endpoint for public customer responses
        endpoint = `/tickets/${targetTicketId}/reply`;
        conversationData = {
          body: sourceConversation.body
        };
      }

      // Handle attachments if present
      if (sourceConversation.attachments && sourceConversation.attachments.length > 0) {
        await this.migrateConversationWithAttachments(
          sourceConversation,
          targetTicketId,
          ticketMigrationId,
          endpoint,
          conversationData
        );
      } else {
        // Create conversation without attachments
        const targetConversationResponse = await this.targetApi.post(endpoint, conversationData);

        // Record conversation migration
        await this.recordConversationMigration(
          ticketMigrationId,
          sourceConversation.id,
          targetConversationResponse.data.id,
          'success'
        );
      }

    } catch (error) {
      logger.error('Error migrating conversation:', error);
      await this.recordConversationMigration(
        ticketMigrationId,
        sourceConversation.id,
        null,
        'failed',
        error.message
      );
    }
  }

  async migrateConversationWithAttachments(sourceConversation, targetTicketId, ticketMigrationId, endpoint, conversationData) {
    const FormData = require('form-data');
    const axios = require('axios');

    try {
      const form = new FormData();

      // Add conversation body
      form.append('body', conversationData.body);

      if (conversationData.private !== undefined) {
        form.append('private', conversationData.private);
      }

      if (conversationData.notify_emails && conversationData.notify_emails.length > 0) {
        conversationData.notify_emails.forEach(email => {
          form.append('notify_emails[]', email);
        });
      }

      // Download and attach files
      for (const attachment of sourceConversation.attachments) {
        try {
          const attachmentResponse = await this.sourceApi.get(`/attachments/${attachment.id}`, {
            responseType: 'stream'
          });

          form.append('attachments[]', attachmentResponse.data, {
            filename: attachment.name,
            contentType: attachment.content_type
          });

        } catch (attachmentError) {
          logger.error('Error downloading attachment:', attachmentError);
          // Continue with other attachments
        }
      }

      // Send request with form data
      const response = await axios.post(
        `${this.targetUrl}${endpoint}`,
        form,
        {
          headers: {
            ...form.getHeaders(),
            'Authorization': `Basic ${Buffer.from(`${this.targetApiKey}:X`).toString('base64')}`
          },
          timeout: 60000 // Longer timeout for file uploads
        }
      );

      // Record successful conversation migration
      await this.recordConversationMigration(
        ticketMigrationId,
        sourceConversation.id,
        response.data.id,
        'success'
      );

    } catch (error) {
      logger.error('Error migrating conversation with attachments:', error);
      await this.recordConversationMigration(
        ticketMigrationId,
        sourceConversation.id,
        null,
        'failed',
        error.message
      );
    }
  }

  async recordConversationMigration(ticketMigrationId, sourceConversationId, targetConversationId, status, errorMessage = null) {
    const db = getDatabase();

    return new Promise((resolve, reject) => {
      const sql = `INSERT INTO conversation_migrations
                   (ticket_migration_id, source_conversation_id, target_conversation_id, status, error_message, migrated_at)
                   VALUES (?, ?, ?, ?, ?, ${status === 'success' ? 'CURRENT_TIMESTAMP' : 'NULL'})`;

      db.run(sql, [ticketMigrationId, sourceConversationId, targetConversationId, status, errorMessage], (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async updateMigrationStatus(status, additionalData = {}) {
    const db = getDatabase();
    const fields = Object.keys(additionalData);
    const values = Object.values(additionalData);
    
    let sql = 'UPDATE migrations SET status = ?';
    const params = [status];
    
    if (fields.length > 0) {
      sql += ', ' + fields.map(field => `${field} = ?`).join(', ');
      params.push(...values);
    }
    
    sql += ' WHERE id = ?';
    params.push(this.migrationId);

    return new Promise((resolve, reject) => {
      db.run(sql, params, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async updateMigrationProgress(processed, successful, failed) {
    return this.updateMigrationStatus('running', {
      processed_tickets: processed,
      successful_tickets: successful,
      failed_tickets: failed
    });
  }

  stop() {
    this.shouldStop = true;
    logger.info('Migration stop requested', { migrationId: this.migrationId });
  }

  async preMigrateContactsAndCompanies(tickets) {
    const uniqueContacts = new Map();
    const uniqueCompanies = new Map();

    // Extract unique contacts and companies from tickets
    for (const ticket of tickets) {
      if (ticket.requester_id) {
        uniqueContacts.set(ticket.requester_id, null);
      }
      if (ticket.company_id) {
        uniqueCompanies.set(ticket.company_id, null);
      }
    }

    // Migrate contacts first
    for (const contactId of uniqueContacts.keys()) {
      if (this.shouldStop) break;
      try {
        await this.migrateContact(contactId);
        await this.delay(this.rateLimit);
      } catch (error) {
        logger.error('Error migrating contact:', error);
        // Continue with other contacts
      }
    }

    // Migrate companies
    for (const companyId of uniqueCompanies.keys()) {
      if (this.shouldStop) break;
      try {
        await this.migrateCompany(companyId);
        await this.delay(this.rateLimit);
      } catch (error) {
        logger.error('Error migrating company:', error);
        // Continue with other companies
      }
    }
  }

  async migrateContact(sourceContactId) {
    const db = getDatabase();

    // Check if contact already migrated
    const existingContact = await new Promise((resolve, reject) => {
      db.get(
        'SELECT target_contact_id FROM contact_migrations WHERE source_contact_id = ? AND status = "success"',
        [sourceContactId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingContact) {
      return existingContact.target_contact_id;
    }

    try {
      // Fetch contact from source
      const sourceContactResponse = await this.sourceApi.get(`/contacts/${sourceContactId}`);
      const sourceContact = sourceContactResponse.data;

      // Transform contact data for target system
      const targetContactData = this.transformContactData(sourceContact);

      // Create contact in target system
      const targetContactResponse = await this.targetApi.post('/contacts', targetContactData);
      const targetContact = targetContactResponse.data;

      // Record contact migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO contact_migrations
           (migration_id, source_contact_id, target_contact_id, email, name, status, migrated_at)
           VALUES (?, ?, ?, ?, ?, 'success', CURRENT_TIMESTAMP)`,
          [this.migrationId, sourceContactId, targetContact.id, sourceContact.email, sourceContact.name],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.info('Contact migrated successfully', {
        migrationId: this.migrationId,
        sourceContactId,
        targetContactId: targetContact.id
      });

      return targetContact.id;

    } catch (error) {
      // Record failed contact migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO contact_migrations
           (migration_id, source_contact_id, status, error_message)
           VALUES (?, ?, 'failed', ?)`,
          [this.migrationId, sourceContactId, error.message],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.error('Contact migration failed', {
        migrationId: this.migrationId,
        sourceContactId,
        error: error.message
      });

      throw error;
    }
  }

  async migrateCompany(sourceCompanyId) {
    const db = getDatabase();

    // Check if company already migrated
    const existingCompany = await new Promise((resolve, reject) => {
      db.get(
        'SELECT target_company_id FROM company_migrations WHERE source_company_id = ? AND status = "success"',
        [sourceCompanyId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });

    if (existingCompany) {
      return existingCompany.target_company_id;
    }

    try {
      // Fetch company from source
      const sourceCompanyResponse = await this.sourceApi.get(`/companies/${sourceCompanyId}`);
      const sourceCompany = sourceCompanyResponse.data;

      // Transform company data for target system
      const targetCompanyData = this.transformCompanyData(sourceCompany);

      // Create company in target system
      const targetCompanyResponse = await this.targetApi.post('/companies', targetCompanyData);
      const targetCompany = targetCompanyResponse.data;

      // Record company migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO company_migrations
           (migration_id, source_company_id, target_company_id, name, domain, status, migrated_at)
           VALUES (?, ?, ?, ?, ?, 'success', CURRENT_TIMESTAMP)`,
          [this.migrationId, sourceCompanyId, targetCompany.id, sourceCompany.name, sourceCompany.domains?.[0]],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.info('Company migrated successfully', {
        migrationId: this.migrationId,
        sourceCompanyId,
        targetCompanyId: targetCompany.id
      });

      return targetCompany.id;

    } catch (error) {
      // Record failed company migration
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO company_migrations
           (migration_id, source_company_id, status, error_message)
           VALUES (?, ?, 'failed', ?)`,
          [this.migrationId, sourceCompanyId, error.message],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      logger.error('Company migration failed', {
        migrationId: this.migrationId,
        sourceCompanyId,
        error: error.message
      });

      throw error;
    }
  }

  transformContactData(sourceContact) {
    return {
      name: sourceContact.name,
      email: sourceContact.email,
      phone: sourceContact.phone,
      mobile: sourceContact.mobile,
      twitter_id: sourceContact.twitter_id,
      unique_external_id: sourceContact.unique_external_id,
      other_emails: sourceContact.other_emails || [],
      company_id: null, // Will be set after company migration
      view_all_tickets: sourceContact.view_all_tickets || false,
      other_companies: sourceContact.other_companies || [],
      address: sourceContact.address,
      avatar: sourceContact.avatar,
      description: sourceContact.description,
      job_title: sourceContact.job_title,
      language: sourceContact.language || 'en',
      tags: sourceContact.tags || [],
      time_zone: sourceContact.time_zone || 'Eastern Time (US & Canada)',
      custom_fields: sourceContact.custom_fields || {}
    };
  }

  transformCompanyData(sourceCompany) {
    return {
      name: sourceCompany.name,
      description: sourceCompany.description,
      note: sourceCompany.note,
      domains: sourceCompany.domains || [],
      health_score: sourceCompany.health_score,
      account_tier: sourceCompany.account_tier,
      renewal_date: sourceCompany.renewal_date,
      industry: sourceCompany.industry,
      custom_fields: sourceCompany.custom_fields || {}
    };
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = MigrationService;
