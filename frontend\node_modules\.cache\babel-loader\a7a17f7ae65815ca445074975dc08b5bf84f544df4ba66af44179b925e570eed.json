{"ast": null, "code": "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "map": {"version": 3, "names": ["modifierPhases", "order", "modifiers", "map", "Map", "visited", "Set", "result", "for<PERSON>ach", "modifier", "set", "name", "sort", "add", "requires", "concat", "requiresIfExists", "dep", "has", "depModifier", "get", "push", "orderModifiers", "orderedModifiers", "reduce", "acc", "phase", "filter"], "sources": ["C:/tictetmigration/frontend/node_modules/@popperjs/core/lib/utils/orderModifiers.js"], "sourcesContent": ["import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}"], "mappings": "AAAA,SAASA,cAAc,QAAQ,aAAa,CAAC,CAAC;;AAE9C,SAASC,KAAKA,CAACC,SAAS,EAAE;EACxB,IAAIC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EACnB,IAAIC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvB,IAAIC,MAAM,GAAG,EAAE;EACfL,SAAS,CAACM,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACpCN,GAAG,CAACO,GAAG,CAACD,QAAQ,CAACE,IAAI,EAAEF,QAAQ,CAAC;EAClC,CAAC,CAAC,CAAC,CAAC;;EAEJ,SAASG,IAAIA,CAACH,QAAQ,EAAE;IACtBJ,OAAO,CAACQ,GAAG,CAACJ,QAAQ,CAACE,IAAI,CAAC;IAC1B,IAAIG,QAAQ,GAAG,EAAE,CAACC,MAAM,CAACN,QAAQ,CAACK,QAAQ,IAAI,EAAE,EAAEL,QAAQ,CAACO,gBAAgB,IAAI,EAAE,CAAC;IAClFF,QAAQ,CAACN,OAAO,CAAC,UAAUS,GAAG,EAAE;MAC9B,IAAI,CAACZ,OAAO,CAACa,GAAG,CAACD,GAAG,CAAC,EAAE;QACrB,IAAIE,WAAW,GAAGhB,GAAG,CAACiB,GAAG,CAACH,GAAG,CAAC;QAE9B,IAAIE,WAAW,EAAE;UACfP,IAAI,CAACO,WAAW,CAAC;QACnB;MACF;IACF,CAAC,CAAC;IACFZ,MAAM,CAACc,IAAI,CAACZ,QAAQ,CAAC;EACvB;EAEAP,SAAS,CAACM,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACpC,IAAI,CAACJ,OAAO,CAACa,GAAG,CAACT,QAAQ,CAACE,IAAI,CAAC,EAAE;MAC/B;MACAC,IAAI,CAACH,QAAQ,CAAC;IAChB;EACF,CAAC,CAAC;EACF,OAAOF,MAAM;AACf;AAEA,eAAe,SAASe,cAAcA,CAACpB,SAAS,EAAE;EAChD;EACA,IAAIqB,gBAAgB,GAAGtB,KAAK,CAACC,SAAS,CAAC,CAAC,CAAC;;EAEzC,OAAOF,cAAc,CAACwB,MAAM,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACjD,OAAOD,GAAG,CAACV,MAAM,CAACQ,gBAAgB,CAACI,MAAM,CAAC,UAAUlB,QAAQ,EAAE;MAC5D,OAAOA,QAAQ,CAACiB,KAAK,KAAKA,KAAK;IACjC,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}