{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaginationItemUtilityClass(slot) {\n  return generateUtilityClass('MuiPaginationItem', slot);\n}\nconst paginationItemClasses = generateUtilityClasses('MuiPaginationItem', ['root', 'page', 'sizeSmall', 'sizeLarge', 'text', 'textPrimary', 'textSecondary', 'outlined', 'outlinedPrimary', 'outlinedSecondary', 'rounded', 'ellipsis', 'firstLast', 'previousNext', 'focusVisible', 'disabled', 'selected', 'icon', 'colorPrimary', 'colorSecondary']);\nexport default paginationItemClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getPaginationItemUtilityClass", "slot", "paginationItemClasses"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/PaginationItem/paginationItemClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaginationItemUtilityClass(slot) {\n  return generateUtilityClass('MuiPaginationItem', slot);\n}\nconst paginationItemClasses = generateUtilityClasses('MuiPaginationItem', ['root', 'page', 'sizeSmall', 'sizeLarge', 'text', 'textPrimary', 'textSecondary', 'outlined', 'outlinedPrimary', 'outlinedSecondary', 'rounded', 'ellipsis', 'firstLast', 'previousNext', 'focusVisible', 'disabled', 'selected', 'icon', 'colorPrimary', 'colorSecondary']);\nexport default paginationItemClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;AACvV,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}