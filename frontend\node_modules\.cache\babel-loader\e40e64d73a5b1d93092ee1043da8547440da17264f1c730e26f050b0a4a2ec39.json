{"ast": null, "code": "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\nfunction hide(_ref) {\n  var state = _ref.state,\n    name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "map": {"version": 3, "names": ["top", "bottom", "left", "right", "detectOverflow", "getSideOffsets", "overflow", "rect", "preventedOffsets", "x", "y", "height", "width", "isAnySideFullyClipped", "some", "side", "hide", "_ref", "state", "name", "referenceRect", "rects", "reference", "popperRect", "popper", "modifiersData", "preventOverflow", "referenceOverflow", "elementContext", "popperAltOverflow", "altBoundary", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "attributes", "Object", "assign", "enabled", "phase", "requiresIfExists", "fn"], "sources": ["C:/tictetmigration/frontend/node_modules/@popperjs/core/lib/modifiers/hide.js"], "sourcesContent": ["import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};"], "mappings": "AAAA,SAASA,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,aAAa;AACtD,OAAOC,cAAc,MAAM,4BAA4B;AAEvD,SAASC,cAAcA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,gBAAgB,EAAE;EACxD,IAAIA,gBAAgB,KAAK,KAAK,CAAC,EAAE;IAC/BA,gBAAgB,GAAG;MACjBC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;EACH;EAEA,OAAO;IACLV,GAAG,EAAEM,QAAQ,CAACN,GAAG,GAAGO,IAAI,CAACI,MAAM,GAAGH,gBAAgB,CAACE,CAAC;IACpDP,KAAK,EAAEG,QAAQ,CAACH,KAAK,GAAGI,IAAI,CAACK,KAAK,GAAGJ,gBAAgB,CAACC,CAAC;IACvDR,MAAM,EAAEK,QAAQ,CAACL,MAAM,GAAGM,IAAI,CAACI,MAAM,GAAGH,gBAAgB,CAACE,CAAC;IAC1DR,IAAI,EAAEI,QAAQ,CAACJ,IAAI,GAAGK,IAAI,CAACK,KAAK,GAAGJ,gBAAgB,CAACC;EACtD,CAAC;AACH;AAEA,SAASI,qBAAqBA,CAACP,QAAQ,EAAE;EACvC,OAAO,CAACN,GAAG,EAAEG,KAAK,EAAEF,MAAM,EAAEC,IAAI,CAAC,CAACY,IAAI,CAAC,UAAUC,IAAI,EAAE;IACrD,OAAOT,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ;AAEA,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB,IAAIC,aAAa,GAAGF,KAAK,CAACG,KAAK,CAACC,SAAS;EACzC,IAAIC,UAAU,GAAGL,KAAK,CAACG,KAAK,CAACG,MAAM;EACnC,IAAIhB,gBAAgB,GAAGU,KAAK,CAACO,aAAa,CAACC,eAAe;EAC1D,IAAIC,iBAAiB,GAAGvB,cAAc,CAACc,KAAK,EAAE;IAC5CU,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,IAAIC,iBAAiB,GAAGzB,cAAc,CAACc,KAAK,EAAE;IAC5CY,WAAW,EAAE;EACf,CAAC,CAAC;EACF,IAAIC,wBAAwB,GAAG1B,cAAc,CAACsB,iBAAiB,EAAEP,aAAa,CAAC;EAC/E,IAAIY,mBAAmB,GAAG3B,cAAc,CAACwB,iBAAiB,EAAEN,UAAU,EAAEf,gBAAgB,CAAC;EACzF,IAAIyB,iBAAiB,GAAGpB,qBAAqB,CAACkB,wBAAwB,CAAC;EACvE,IAAIG,gBAAgB,GAAGrB,qBAAqB,CAACmB,mBAAmB,CAAC;EACjEd,KAAK,CAACO,aAAa,CAACN,IAAI,CAAC,GAAG;IAC1BY,wBAAwB,EAAEA,wBAAwB;IAClDC,mBAAmB,EAAEA,mBAAmB;IACxCC,iBAAiB,EAAEA,iBAAiB;IACpCC,gBAAgB,EAAEA;EACpB,CAAC;EACDhB,KAAK,CAACiB,UAAU,CAACX,MAAM,GAAGY,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnB,KAAK,CAACiB,UAAU,CAACX,MAAM,EAAE;IACnE,8BAA8B,EAAES,iBAAiB;IACjD,qBAAqB,EAAEC;EACzB,CAAC,CAAC;AACJ,CAAC,CAAC;;AAGF,eAAe;EACbf,IAAI,EAAE,MAAM;EACZmB,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,MAAM;EACbC,gBAAgB,EAAE,CAAC,iBAAiB,CAAC;EACrCC,EAAE,EAAEzB;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}