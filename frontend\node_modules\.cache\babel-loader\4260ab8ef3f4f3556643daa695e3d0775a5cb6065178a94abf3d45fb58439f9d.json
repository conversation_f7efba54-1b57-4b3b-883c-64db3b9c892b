{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits } from \"../utils.js\";\nexport var QuarterParser = /*#__PURE__*/function (_Parser) {\n  _inherits(QuarterParser, _Parser);\n  var _super = _createSuper(QuarterParser);\n  function QuarterParser() {\n    var _this;\n    _classCallCheck(this, QuarterParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 120);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['Y', 'R', 'q', 'M', 'L', 'w', 'I', 'd', 'D', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(QuarterParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        // 1, 2, 3, 4\n        case 'Q':\n        case 'QQ':\n          // 01, 02, 03, 04\n          return parseNDigits(token.length, dateString);\n        // 1st, 2nd, 3rd, 4th\n        case 'Qo':\n          return match.ordinalNumber(dateString, {\n            unit: 'quarter'\n          });\n        // Q1, Q2, Q3, Q4\n        case 'QQQ':\n          return match.quarter(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n        case 'QQQQQ':\n          return match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // 1st quarter, 2nd quarter, ...\n        case 'QQQQ':\n        default:\n          return match.quarter(dateString, {\n            width: 'wide',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 1 && value <= 4;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCMonth((value - 1) * 3, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return QuarterParser;\n}(Parser);", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "<PERSON><PERSON><PERSON>", "parseNDigits", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "key", "value", "parse", "dateString", "token", "match", "ordinalNumber", "unit", "quarter", "width", "context", "validate", "_date", "set", "date", "_flags", "setUTCMonth", "setUTCHours"], "sources": ["C:/tictetmigration/frontend/node_modules/date-fns/esm/parse/_lib/parsers/QuarterParser.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits } from \"../utils.js\";\nexport var QuarterParser = /*#__PURE__*/function (_Parser) {\n  _inherits(QuarterParser, _Parser);\n  var _super = _createSuper(QuarterParser);\n  function QuarterParser() {\n    var _this;\n    _classCallCheck(this, QuarterParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 120);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['Y', 'R', 'q', 'M', 'L', 'w', 'I', 'd', 'D', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(QuarterParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        // 1, 2, 3, 4\n        case 'Q':\n        case 'QQ':\n          // 01, 02, 03, 04\n          return parseNDigits(token.length, dateString);\n        // 1st, 2nd, 3rd, 4th\n        case 'Qo':\n          return match.ordinalNumber(dateString, {\n            unit: 'quarter'\n          });\n        // Q1, Q2, Q3, Q4\n        case 'QQQ':\n          return match.quarter(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n        case 'QQQQQ':\n          return match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // 1st quarter, 2nd quarter, ...\n        case 'QQQQ':\n        default:\n          return match.quarter(dateString, {\n            width: 'wide',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 1 && value <= 4;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCMonth((value - 1) * 3, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return QuarterParser;\n}(Parser);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,YAAY,QAAQ,aAAa;AAC1C,OAAO,IAAIC,aAAa,GAAG,aAAa,UAAUC,OAAO,EAAE;EACzDN,SAAS,CAACK,aAAa,EAAEC,OAAO,CAAC;EACjC,IAAIC,MAAM,GAAGN,YAAY,CAACI,aAAa,CAAC;EACxC,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAIG,KAAK;IACTX,eAAe,CAAC,IAAI,EAAEQ,aAAa,CAAC;IACpC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDV,eAAe,CAACH,sBAAsB,CAACS,KAAK,CAAC,EAAE,UAAU,EAAE,GAAG,CAAC;IAC/DN,eAAe,CAACH,sBAAsB,CAACS,KAAK,CAAC,EAAE,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC5I,OAAOA,KAAK;EACd;EACAV,YAAY,CAACO,aAAa,EAAE,CAAC;IAC3Ba,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;MAC9C,QAAQD,KAAK;QACX;QACA,KAAK,GAAG;QACR,KAAK,IAAI;UACP;UACA,OAAOlB,YAAY,CAACkB,KAAK,CAACX,MAAM,EAAEU,UAAU,CAAC;QAC/C;QACA,KAAK,IAAI;UACP,OAAOE,KAAK,CAACC,aAAa,CAACH,UAAU,EAAE;YACrCI,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;QACA,KAAK,KAAK;UACR,OAAOF,KAAK,CAACG,OAAO,CAACL,UAAU,EAAE;YAC/BM,KAAK,EAAE,aAAa;YACpBC,OAAO,EAAE;UACX,CAAC,CAAC,IAAIL,KAAK,CAACG,OAAO,CAACL,UAAU,EAAE;YAC9BM,KAAK,EAAE,QAAQ;YACfC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QACA,KAAK,OAAO;UACV,OAAOL,KAAK,CAACG,OAAO,CAACL,UAAU,EAAE;YAC/BM,KAAK,EAAE,QAAQ;YACfC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QACA,KAAK,MAAM;QACX;UACE,OAAOL,KAAK,CAACG,OAAO,CAACL,UAAU,EAAE;YAC/BM,KAAK,EAAE,MAAM;YACbC,OAAO,EAAE;UACX,CAAC,CAAC,IAAIL,KAAK,CAACG,OAAO,CAACL,UAAU,EAAE;YAC9BM,KAAK,EAAE,aAAa;YACpBC,OAAO,EAAE;UACX,CAAC,CAAC,IAAIL,KAAK,CAACG,OAAO,CAACL,UAAU,EAAE;YAC9BM,KAAK,EAAE,QAAQ;YACfC,OAAO,EAAE;UACX,CAAC,CAAC;MACN;IACF;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,SAASU,QAAQA,CAACC,KAAK,EAAEX,KAAK,EAAE;MACrC,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC;IACjC;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASY,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEd,KAAK,EAAE;MACvCa,IAAI,CAACE,WAAW,CAAC,CAACf,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACpCa,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5B,OAAOH,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAO3B,aAAa;AACtB,CAAC,CAACF,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}