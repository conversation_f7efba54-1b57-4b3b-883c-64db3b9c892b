{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\n\n// prepend: true moves MUI styles to the top of the <head> so they're loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getCache(injectFirst, enableCssLayer) {\n  const emotionCache = createCache({\n    key: 'css',\n    prepend: injectFirst\n  });\n  if (enableCssLayer) {\n    const prevInsert = emotionCache.insert;\n    emotionCache.insert = (...args) => {\n      if (!args[1].styles.match(/^@layer\\s+[^{]*$/)) {\n        // avoid nested @layer\n        args[1].styles = `@layer mui {${args[1].styles}}`;\n      }\n      return prevInsert(...args);\n    };\n  }\n  return emotionCache;\n}\nconst cacheMap = new Map();\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    enableCssLayer,\n    children\n  } = props;\n  const cache = React.useMemo(() => {\n    const cacheKey = `${injectFirst}-${enableCssLayer}`;\n    if (typeof document === 'object' && cacheMap.has(cacheKey)) {\n      return cacheMap.get(cacheKey);\n    }\n    const fresh = getCache(injectFirst, enableCssLayer);\n    cacheMap.set(cacheKey, fresh);\n    return fresh;\n  }, [injectFirst, enableCssLayer]);\n  if (injectFirst || enableCssLayer) {\n    return /*#__PURE__*/_jsx(CacheProvider, {\n      value: cache,\n      children: children\n    });\n  }\n  return children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * If true, MUI styles are wrapped in CSS `@layer mui` rule.\n   * It helps to override MUI styles when using CSS Modules, Tailwind CSS, plain CSS, or any other styling solution.\n   */\n  enableCssLayer: PropTypes.bool,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;", "map": {"version": 3, "names": ["React", "PropTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createCache", "jsx", "_jsx", "getCache", "injectFirst", "enableCssLayer", "emotionCache", "key", "prepend", "prevInsert", "insert", "args", "styles", "match", "cacheMap", "Map", "StyledEngineProvider", "props", "children", "cache", "useMemo", "cache<PERSON>ey", "document", "has", "get", "fresh", "set", "value", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\n\n// prepend: true moves MUI styles to the top of the <head> so they're loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getCache(injectFirst, enableCssLayer) {\n  const emotionCache = createCache({\n    key: 'css',\n    prepend: injectFirst\n  });\n  if (enableCssLayer) {\n    const prevInsert = emotionCache.insert;\n    emotionCache.insert = (...args) => {\n      if (!args[1].styles.match(/^@layer\\s+[^{]*$/)) {\n        // avoid nested @layer\n        args[1].styles = `@layer mui {${args[1].styles}}`;\n      }\n      return prevInsert(...args);\n    };\n  }\n  return emotionCache;\n}\nconst cacheMap = new Map();\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    enableCssLayer,\n    children\n  } = props;\n  const cache = React.useMemo(() => {\n    const cacheKey = `${injectFirst}-${enableCssLayer}`;\n    if (typeof document === 'object' && cacheMap.has(cacheKey)) {\n      return cacheMap.get(cacheKey);\n    }\n    const fresh = getCache(injectFirst, enableCssLayer);\n    cacheMap.set(cacheKey, fresh);\n    return fresh;\n  }, [injectFirst, enableCssLayer]);\n  if (injectFirst || enableCssLayer) {\n    return /*#__PURE__*/_jsx(CacheProvider, {\n      value: cache,\n      children: children\n    });\n  }\n  return children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * If true, MUI styles are wrapped in CSS `@layer mui` rule.\n   * It helps to override MUI styles when using CSS Modules, Tailwind CSS, plain CSS, or any other styling solution.\n   */\n  enableCssLayer: PropTypes.bool,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,WAAW,MAAM,gBAAgB;;AAExC;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQA,CAACC,WAAW,EAAEC,cAAc,EAAE;EAC7C,MAAMC,YAAY,GAAGN,WAAW,CAAC;IAC/BO,GAAG,EAAE,KAAK;IACVC,OAAO,EAAEJ;EACX,CAAC,CAAC;EACF,IAAIC,cAAc,EAAE;IAClB,MAAMI,UAAU,GAAGH,YAAY,CAACI,MAAM;IACtCJ,YAAY,CAACI,MAAM,GAAG,CAAC,GAAGC,IAAI,KAAK;MACjC,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,kBAAkB,CAAC,EAAE;QAC7C;QACAF,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG,eAAeD,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG;MACnD;MACA,OAAOH,UAAU,CAAC,GAAGE,IAAI,CAAC;IAC5B,CAAC;EACH;EACA,OAAOL,YAAY;AACrB;AACA,MAAMQ,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC1B,eAAe,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAClD,MAAM;IACJb,WAAW;IACXC,cAAc;IACda;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,KAAK,GAAGtB,KAAK,CAACuB,OAAO,CAAC,MAAM;IAChC,MAAMC,QAAQ,GAAG,GAAGjB,WAAW,IAAIC,cAAc,EAAE;IACnD,IAAI,OAAOiB,QAAQ,KAAK,QAAQ,IAAIR,QAAQ,CAACS,GAAG,CAACF,QAAQ,CAAC,EAAE;MAC1D,OAAOP,QAAQ,CAACU,GAAG,CAACH,QAAQ,CAAC;IAC/B;IACA,MAAMI,KAAK,GAAGtB,QAAQ,CAACC,WAAW,EAAEC,cAAc,CAAC;IACnDS,QAAQ,CAACY,GAAG,CAACL,QAAQ,EAAEI,KAAK,CAAC;IAC7B,OAAOA,KAAK;EACd,CAAC,EAAE,CAACrB,WAAW,EAAEC,cAAc,CAAC,CAAC;EACjC,IAAID,WAAW,IAAIC,cAAc,EAAE;IACjC,OAAO,aAAaH,IAAI,CAACH,aAAa,EAAE;MACtC4B,KAAK,EAAER,KAAK;MACZD,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ;EACA,OAAOA,QAAQ;AACjB;AACAU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGd,oBAAoB,CAACe,SAAS,GAAG;EACvE;AACF;AACA;EACEb,QAAQ,EAAEpB,SAAS,CAACkC,IAAI;EACxB;AACF;AACA;AACA;EACE3B,cAAc,EAAEP,SAAS,CAACmC,IAAI;EAC9B;AACF;AACA;AACA;AACA;EACE7B,WAAW,EAAEN,SAAS,CAACmC;AACzB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}