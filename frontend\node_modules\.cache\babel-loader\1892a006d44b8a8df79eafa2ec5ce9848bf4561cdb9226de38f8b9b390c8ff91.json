{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param {object} defaultProps\n * @param {object} props\n * @returns {object} resolved props\n */\nexport default function resolveProps(defaultProps, props) {\n  const output = _extends({}, props);\n  Object.keys(defaultProps).forEach(propName => {\n    if (propName.toString().match(/^(components|slots)$/)) {\n      output[propName] = _extends({}, defaultProps[propName], output[propName]);\n    } else if (propName.toString().match(/^(componentsProps|slotProps)$/)) {\n      const defaultSlotProps = defaultProps[propName] || {};\n      const slotProps = props[propName];\n      output[propName] = {};\n      if (!slotProps || !Object.keys(slotProps)) {\n        // Reduce the iteration if the slot props is empty\n        output[propName] = defaultSlotProps;\n      } else if (!defaultSlotProps || !Object.keys(defaultSlotProps)) {\n        // Reduce the iteration if the default slot props is empty\n        output[propName] = slotProps;\n      } else {\n        output[propName] = _extends({}, slotProps);\n        Object.keys(defaultSlotProps).forEach(slotPropName => {\n          output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName]);\n        });\n      }\n    } else if (output[propName] === undefined) {\n      output[propName] = defaultProps[propName];\n    }\n  });\n  return output;\n}", "map": {"version": 3, "names": ["_extends", "resolveProps", "defaultProps", "props", "output", "Object", "keys", "for<PERSON>ach", "propName", "toString", "match", "defaultSlotProps", "slotProps", "slotPropName", "undefined"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/utils/esm/resolveProps/resolveProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param {object} defaultProps\n * @param {object} props\n * @returns {object} resolved props\n */\nexport default function resolveProps(defaultProps, props) {\n  const output = _extends({}, props);\n  Object.keys(defaultProps).forEach(propName => {\n    if (propName.toString().match(/^(components|slots)$/)) {\n      output[propName] = _extends({}, defaultProps[propName], output[propName]);\n    } else if (propName.toString().match(/^(componentsProps|slotProps)$/)) {\n      const defaultSlotProps = defaultProps[propName] || {};\n      const slotProps = props[propName];\n      output[propName] = {};\n      if (!slotProps || !Object.keys(slotProps)) {\n        // Reduce the iteration if the slot props is empty\n        output[propName] = defaultSlotProps;\n      } else if (!defaultSlotProps || !Object.keys(defaultSlotProps)) {\n        // Reduce the iteration if the default slot props is empty\n        output[propName] = slotProps;\n      } else {\n        output[propName] = _extends({}, slotProps);\n        Object.keys(defaultSlotProps).forEach(slotPropName => {\n          output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName]);\n        });\n      }\n    } else if (output[propName] === undefined) {\n      output[propName] = defaultProps[propName];\n    }\n  });\n  return output;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,KAAK,EAAE;EACxD,MAAMC,MAAM,GAAGJ,QAAQ,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC;EAClCE,MAAM,CAACC,IAAI,CAACJ,YAAY,CAAC,CAACK,OAAO,CAACC,QAAQ,IAAI;IAC5C,IAAIA,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,sBAAsB,CAAC,EAAE;MACrDN,MAAM,CAACI,QAAQ,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAEE,YAAY,CAACM,QAAQ,CAAC,EAAEJ,MAAM,CAACI,QAAQ,CAAC,CAAC;IAC3E,CAAC,MAAM,IAAIA,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,+BAA+B,CAAC,EAAE;MACrE,MAAMC,gBAAgB,GAAGT,YAAY,CAACM,QAAQ,CAAC,IAAI,CAAC,CAAC;MACrD,MAAMI,SAAS,GAAGT,KAAK,CAACK,QAAQ,CAAC;MACjCJ,MAAM,CAACI,QAAQ,CAAC,GAAG,CAAC,CAAC;MACrB,IAAI,CAACI,SAAS,IAAI,CAACP,MAAM,CAACC,IAAI,CAACM,SAAS,CAAC,EAAE;QACzC;QACAR,MAAM,CAACI,QAAQ,CAAC,GAAGG,gBAAgB;MACrC,CAAC,MAAM,IAAI,CAACA,gBAAgB,IAAI,CAACN,MAAM,CAACC,IAAI,CAACK,gBAAgB,CAAC,EAAE;QAC9D;QACAP,MAAM,CAACI,QAAQ,CAAC,GAAGI,SAAS;MAC9B,CAAC,MAAM;QACLR,MAAM,CAACI,QAAQ,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAEY,SAAS,CAAC;QAC1CP,MAAM,CAACC,IAAI,CAACK,gBAAgB,CAAC,CAACJ,OAAO,CAACM,YAAY,IAAI;UACpDT,MAAM,CAACI,QAAQ,CAAC,CAACK,YAAY,CAAC,GAAGZ,YAAY,CAACU,gBAAgB,CAACE,YAAY,CAAC,EAAED,SAAS,CAACC,YAAY,CAAC,CAAC;QACxG,CAAC,CAAC;MACJ;IACF,CAAC,MAAM,IAAIT,MAAM,CAACI,QAAQ,CAAC,KAAKM,SAAS,EAAE;MACzCV,MAAM,CAACI,QAAQ,CAAC,GAAGN,YAAY,CAACM,QAAQ,CAAC;IAC3C;EACF,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}