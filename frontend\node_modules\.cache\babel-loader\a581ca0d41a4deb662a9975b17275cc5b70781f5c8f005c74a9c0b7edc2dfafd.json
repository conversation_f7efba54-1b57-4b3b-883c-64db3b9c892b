{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"anchorEl\", \"children\", \"direction\", \"disablePortal\", \"modifiers\", \"open\", \"placement\", \"popperOptions\", \"popperRef\", \"slotProps\", \"slots\", \"TransitionProps\", \"ownerState\"],\n  _excluded2 = [\"anchorEl\", \"children\", \"container\", \"direction\", \"disablePortal\", \"keepMounted\", \"modifiers\", \"open\", \"placement\", \"popperOptions\", \"popperRef\", \"style\", \"transition\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport { chainPropTypes, HTMLElementType, refType, unstable_ownerDocument as ownerDocument, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { createPopper } from '@popperjs/core';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport Portal from '../Portal';\nimport { getPopperUtilityClass } from './popperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction flipPlacement(placement, direction) {\n  if (direction === 'ltr') {\n    return placement;\n  }\n  switch (placement) {\n    case 'bottom-end':\n      return 'bottom-start';\n    case 'bottom-start':\n      return 'bottom-end';\n    case 'top-end':\n      return 'top-start';\n    case 'top-start':\n      return 'top-end';\n    default:\n      return placement;\n  }\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nfunction isHTMLElement(element) {\n  return element.nodeType !== undefined;\n}\nfunction isVirtualElement(element) {\n  return !isHTMLElement(element);\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getPopperUtilityClass, classes);\n};\nconst defaultPopperOptions = {};\nconst PopperTooltip = /*#__PURE__*/React.forwardRef(function PopperTooltip(props, forwardedRef) {\n  var _slots$root;\n  const {\n      anchorEl,\n      children,\n      direction,\n      disablePortal,\n      modifiers,\n      open,\n      placement: initialPlacement,\n      popperOptions,\n      popperRef: popperRefProp,\n      slotProps = {},\n      slots = {},\n      TransitionProps\n      // @ts-ignore internal logic\n      // prevent from spreading to DOM, it can come from the parent component e.g. Select.\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const tooltipRef = React.useRef(null);\n  const ownRef = useForkRef(tooltipRef, forwardedRef);\n  const popperRef = React.useRef(null);\n  const handlePopperRef = useForkRef(popperRef, popperRefProp);\n  const handlePopperRefRef = React.useRef(handlePopperRef);\n  useEnhancedEffect(() => {\n    handlePopperRefRef.current = handlePopperRef;\n  }, [handlePopperRef]);\n  React.useImperativeHandle(popperRefProp, () => popperRef.current, []);\n  const rtlPlacement = flipPlacement(initialPlacement, direction);\n  /**\n   * placement initialized from prop but can change during lifetime if modifiers.flip.\n   * modifiers.flip is essentially a flip for controlled/uncontrolled behavior\n   */\n  const [placement, setPlacement] = React.useState(rtlPlacement);\n  const [resolvedAnchorElement, setResolvedAnchorElement] = React.useState(resolveAnchorEl(anchorEl));\n  React.useEffect(() => {\n    if (popperRef.current) {\n      popperRef.current.forceUpdate();\n    }\n  });\n  React.useEffect(() => {\n    if (anchorEl) {\n      setResolvedAnchorElement(resolveAnchorEl(anchorEl));\n    }\n  }, [anchorEl]);\n  useEnhancedEffect(() => {\n    if (!resolvedAnchorElement || !open) {\n      return undefined;\n    }\n    const handlePopperUpdate = data => {\n      setPlacement(data.placement);\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      if (resolvedAnchorElement && isHTMLElement(resolvedAnchorElement) && resolvedAnchorElement.nodeType === 1) {\n        const box = resolvedAnchorElement.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      }\n    }\n    let popperModifiers = [{\n      name: 'preventOverflow',\n      options: {\n        altBoundary: disablePortal\n      }\n    }, {\n      name: 'flip',\n      options: {\n        altBoundary: disablePortal\n      }\n    }, {\n      name: 'onUpdate',\n      enabled: true,\n      phase: 'afterWrite',\n      fn: ({\n        state\n      }) => {\n        handlePopperUpdate(state);\n      }\n    }];\n    if (modifiers != null) {\n      popperModifiers = popperModifiers.concat(modifiers);\n    }\n    if (popperOptions && popperOptions.modifiers != null) {\n      popperModifiers = popperModifiers.concat(popperOptions.modifiers);\n    }\n    const popper = createPopper(resolvedAnchorElement, tooltipRef.current, _extends({\n      placement: rtlPlacement\n    }, popperOptions, {\n      modifiers: popperModifiers\n    }));\n    handlePopperRefRef.current(popper);\n    return () => {\n      popper.destroy();\n      handlePopperRefRef.current(null);\n    };\n  }, [resolvedAnchorElement, disablePortal, modifiers, open, popperOptions, rtlPlacement]);\n  const childProps = {\n    placement: placement\n  };\n  if (TransitionProps !== null) {\n    childProps.TransitionProps = TransitionProps;\n  }\n  const classes = useUtilityClasses(props);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      role: 'tooltip',\n      ref: ownRef\n    },\n    ownerState: props,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: typeof children === 'function' ? children(childProps) : children\n  }));\n});\n\n/**\n * @ignore - internal component.\n */\nconst Popper = /*#__PURE__*/React.forwardRef(function Popper(props, forwardedRef) {\n  const {\n      anchorEl,\n      children,\n      container: containerProp,\n      direction = 'ltr',\n      disablePortal = false,\n      keepMounted = false,\n      modifiers,\n      open,\n      placement = 'bottom',\n      popperOptions = defaultPopperOptions,\n      popperRef,\n      style,\n      transition = false,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [exited, setExited] = React.useState(true);\n  const handleEnter = () => {\n    setExited(false);\n  };\n  const handleExited = () => {\n    setExited(true);\n  };\n  if (!keepMounted && !open && (!transition || exited)) {\n    return null;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  let container;\n  if (containerProp) {\n    container = containerProp;\n  } else if (anchorEl) {\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n    container = resolvedAnchorEl && isHTMLElement(resolvedAnchorEl) ? ownerDocument(resolvedAnchorEl).body : ownerDocument(null).body;\n  }\n  const display = !open && keepMounted && (!transition || exited) ? 'none' : undefined;\n  const transitionProps = transition ? {\n    in: open,\n    onEnter: handleEnter,\n    onExited: handleExited\n  } : undefined;\n  return /*#__PURE__*/_jsx(Portal, {\n    disablePortal: disablePortal,\n    container: container,\n    children: /*#__PURE__*/_jsx(PopperTooltip, _extends({\n      anchorEl: anchorEl,\n      direction: direction,\n      disablePortal: disablePortal,\n      modifiers: modifiers,\n      ref: forwardedRef,\n      open: transition ? !exited : open,\n      placement: placement,\n      popperOptions: popperOptions,\n      popperRef: popperRef,\n      slotProps: slotProps,\n      slots: slots\n    }, other, {\n      style: _extends({\n        // Prevents scroll issue, waiting for Popper.js to add this style once initiated.\n        position: 'fixed',\n        // Fix Popper.js display issue\n        top: 0,\n        left: 0,\n        display\n      }, style),\n      TransitionProps: transitionProps,\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Popper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * An HTML element, [virtualElement](https://popper.js.org/docs/v2/virtual-elements/),\n   * or a function that returns either.\n   * It's used to set the position of the popper.\n   * The return value will passed as the reference object of the Popper instance.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]), props => {\n    if (props.open) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && isHTMLElement(resolvedAnchorEl) && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else if (!resolvedAnchorEl || typeof resolvedAnchorEl.getBoundingClientRect !== 'function' || isVirtualElement(resolvedAnchorEl) && resolvedAnchorEl.contextElement != null && resolvedAnchorEl.contextElement.nodeType !== 1) {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'It should be an HTML element instance or a virtualElement ', '(https://popper.js.org/docs/v2/virtual-elements/).'].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * Popper render function or node.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.node, PropTypes.func]),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Direction of the text.\n   * @default 'ltr'\n   */\n  direction: PropTypes.oneOf(['ltr', 'rtl']),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Popper.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Popper.js is based on a \"plugin-like\" architecture,\n   * most of its features are fully encapsulated \"modifiers\".\n   *\n   * A modifier is a function that is called each time Popper.js needs to\n   * compute the position of the popper.\n   * For this reason, modifiers should be very performant to avoid bottlenecks.\n   * To learn how to create a modifier, [read the modifiers documentation](https://popper.js.org/docs/v2/modifiers/).\n   */\n  modifiers: PropTypes.arrayOf(PropTypes.shape({\n    data: PropTypes.object,\n    effect: PropTypes.func,\n    enabled: PropTypes.bool,\n    fn: PropTypes.func,\n    name: PropTypes.any,\n    options: PropTypes.object,\n    phase: PropTypes.oneOf(['afterMain', 'afterRead', 'afterWrite', 'beforeMain', 'beforeRead', 'beforeWrite', 'main', 'read', 'write']),\n    requires: PropTypes.arrayOf(PropTypes.string),\n    requiresIfExists: PropTypes.arrayOf(PropTypes.string)\n  })),\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Popper placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * Options provided to the [`Popper.js`](https://popper.js.org/docs/v2/constructors/#options) instance.\n   * @default {}\n   */\n  popperOptions: PropTypes.shape({\n    modifiers: PropTypes.array,\n    onFirstUpdate: PropTypes.func,\n    placement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n    strategy: PropTypes.oneOf(['absolute', 'fixed'])\n  }),\n  /**\n   * A ref that points to the used popper instance.\n   */\n  popperRef: refType,\n  /**\n   * The props used for each slot inside the Popper.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Popper.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * Help supporting a react-transition-group/Transition component.\n   * @default false\n   */\n  transition: PropTypes.bool\n} : void 0;\nexport default Popper;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "chainPropTypes", "HTMLElementType", "refType", "unstable_ownerDocument", "ownerDocument", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_useForkRef", "useForkRef", "createPopper", "PropTypes", "composeClasses", "useSlotProps", "Portal", "getPopperUtilityClass", "jsx", "_jsx", "flipPlacement", "placement", "direction", "resolveAnchorEl", "anchorEl", "isHTMLElement", "element", "nodeType", "undefined", "isVirtualElement", "useUtilityClasses", "ownerState", "classes", "slots", "root", "defaultPopperOptions", "PopperTooltip", "forwardRef", "props", "forwardedRef", "_slots$root", "children", "disable<PERSON><PERSON><PERSON>", "modifiers", "open", "initialPlacement", "popperOptions", "popperRef", "popperRefProp", "slotProps", "TransitionProps", "other", "tooltipRef", "useRef", "ownRef", "handlePopperRef", "handlePopperRefRef", "current", "useImperativeHandle", "rtlPlacement", "setPlacement", "useState", "resolvedAnchorElement", "setResolvedAnchorElement", "useEffect", "forceUpdate", "handlePopperUpdate", "data", "process", "env", "NODE_ENV", "box", "getBoundingClientRect", "top", "left", "right", "bottom", "console", "warn", "join", "popperModifiers", "name", "options", "altBoundary", "enabled", "phase", "fn", "state", "concat", "popper", "destroy", "childProps", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "role", "ref", "className", "<PERSON><PERSON>", "container", "containerProp", "keepMounted", "style", "transition", "exited", "setExited", "handleEnter", "handleExited", "resolvedAnchorEl", "body", "display", "transitionProps", "in", "onEnter", "onExited", "position", "propTypes", "oneOfType", "object", "func", "Error", "contextElement", "node", "oneOf", "bool", "arrayOf", "shape", "effect", "any", "requires", "string", "requiresIfExists", "isRequired", "array", "onFirstUpdate", "strategy"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/Popper/BasePopper.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"anchorEl\", \"children\", \"direction\", \"disablePortal\", \"modifiers\", \"open\", \"placement\", \"popperOptions\", \"popperRef\", \"slotProps\", \"slots\", \"TransitionProps\", \"ownerState\"],\n  _excluded2 = [\"anchorEl\", \"children\", \"container\", \"direction\", \"disablePortal\", \"keepMounted\", \"modifiers\", \"open\", \"placement\", \"popperOptions\", \"popperRef\", \"style\", \"transition\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport { chainPropTypes, HTMLElementType, refType, unstable_ownerDocument as ownerDocument, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { createPopper } from '@popperjs/core';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport Portal from '../Portal';\nimport { getPopperUtilityClass } from './popperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction flipPlacement(placement, direction) {\n  if (direction === 'ltr') {\n    return placement;\n  }\n  switch (placement) {\n    case 'bottom-end':\n      return 'bottom-start';\n    case 'bottom-start':\n      return 'bottom-end';\n    case 'top-end':\n      return 'top-start';\n    case 'top-start':\n      return 'top-end';\n    default:\n      return placement;\n  }\n}\nfunction resolveAnchorEl(anchorEl) {\n  return typeof anchorEl === 'function' ? anchorEl() : anchorEl;\n}\nfunction isHTMLElement(element) {\n  return element.nodeType !== undefined;\n}\nfunction isVirtualElement(element) {\n  return !isHTMLElement(element);\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getPopperUtilityClass, classes);\n};\nconst defaultPopperOptions = {};\nconst PopperTooltip = /*#__PURE__*/React.forwardRef(function PopperTooltip(props, forwardedRef) {\n  var _slots$root;\n  const {\n      anchorEl,\n      children,\n      direction,\n      disablePortal,\n      modifiers,\n      open,\n      placement: initialPlacement,\n      popperOptions,\n      popperRef: popperRefProp,\n      slotProps = {},\n      slots = {},\n      TransitionProps\n      // @ts-ignore internal logic\n      // prevent from spreading to DOM, it can come from the parent component e.g. Select.\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const tooltipRef = React.useRef(null);\n  const ownRef = useForkRef(tooltipRef, forwardedRef);\n  const popperRef = React.useRef(null);\n  const handlePopperRef = useForkRef(popperRef, popperRefProp);\n  const handlePopperRefRef = React.useRef(handlePopperRef);\n  useEnhancedEffect(() => {\n    handlePopperRefRef.current = handlePopperRef;\n  }, [handlePopperRef]);\n  React.useImperativeHandle(popperRefProp, () => popperRef.current, []);\n  const rtlPlacement = flipPlacement(initialPlacement, direction);\n  /**\n   * placement initialized from prop but can change during lifetime if modifiers.flip.\n   * modifiers.flip is essentially a flip for controlled/uncontrolled behavior\n   */\n  const [placement, setPlacement] = React.useState(rtlPlacement);\n  const [resolvedAnchorElement, setResolvedAnchorElement] = React.useState(resolveAnchorEl(anchorEl));\n  React.useEffect(() => {\n    if (popperRef.current) {\n      popperRef.current.forceUpdate();\n    }\n  });\n  React.useEffect(() => {\n    if (anchorEl) {\n      setResolvedAnchorElement(resolveAnchorEl(anchorEl));\n    }\n  }, [anchorEl]);\n  useEnhancedEffect(() => {\n    if (!resolvedAnchorElement || !open) {\n      return undefined;\n    }\n    const handlePopperUpdate = data => {\n      setPlacement(data.placement);\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      if (resolvedAnchorElement && isHTMLElement(resolvedAnchorElement) && resolvedAnchorElement.nodeType === 1) {\n        const box = resolvedAnchorElement.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          console.warn(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      }\n    }\n    let popperModifiers = [{\n      name: 'preventOverflow',\n      options: {\n        altBoundary: disablePortal\n      }\n    }, {\n      name: 'flip',\n      options: {\n        altBoundary: disablePortal\n      }\n    }, {\n      name: 'onUpdate',\n      enabled: true,\n      phase: 'afterWrite',\n      fn: ({\n        state\n      }) => {\n        handlePopperUpdate(state);\n      }\n    }];\n    if (modifiers != null) {\n      popperModifiers = popperModifiers.concat(modifiers);\n    }\n    if (popperOptions && popperOptions.modifiers != null) {\n      popperModifiers = popperModifiers.concat(popperOptions.modifiers);\n    }\n    const popper = createPopper(resolvedAnchorElement, tooltipRef.current, _extends({\n      placement: rtlPlacement\n    }, popperOptions, {\n      modifiers: popperModifiers\n    }));\n    handlePopperRefRef.current(popper);\n    return () => {\n      popper.destroy();\n      handlePopperRefRef.current(null);\n    };\n  }, [resolvedAnchorElement, disablePortal, modifiers, open, popperOptions, rtlPlacement]);\n  const childProps = {\n    placement: placement\n  };\n  if (TransitionProps !== null) {\n    childProps.TransitionProps = TransitionProps;\n  }\n  const classes = useUtilityClasses(props);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      role: 'tooltip',\n      ref: ownRef\n    },\n    ownerState: props,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: typeof children === 'function' ? children(childProps) : children\n  }));\n});\n\n/**\n * @ignore - internal component.\n */\nconst Popper = /*#__PURE__*/React.forwardRef(function Popper(props, forwardedRef) {\n  const {\n      anchorEl,\n      children,\n      container: containerProp,\n      direction = 'ltr',\n      disablePortal = false,\n      keepMounted = false,\n      modifiers,\n      open,\n      placement = 'bottom',\n      popperOptions = defaultPopperOptions,\n      popperRef,\n      style,\n      transition = false,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const [exited, setExited] = React.useState(true);\n  const handleEnter = () => {\n    setExited(false);\n  };\n  const handleExited = () => {\n    setExited(true);\n  };\n  if (!keepMounted && !open && (!transition || exited)) {\n    return null;\n  }\n\n  // If the container prop is provided, use that\n  // If the anchorEl prop is provided, use its parent body element as the container\n  // If neither are provided let the Modal take care of choosing the container\n  let container;\n  if (containerProp) {\n    container = containerProp;\n  } else if (anchorEl) {\n    const resolvedAnchorEl = resolveAnchorEl(anchorEl);\n    container = resolvedAnchorEl && isHTMLElement(resolvedAnchorEl) ? ownerDocument(resolvedAnchorEl).body : ownerDocument(null).body;\n  }\n  const display = !open && keepMounted && (!transition || exited) ? 'none' : undefined;\n  const transitionProps = transition ? {\n    in: open,\n    onEnter: handleEnter,\n    onExited: handleExited\n  } : undefined;\n  return /*#__PURE__*/_jsx(Portal, {\n    disablePortal: disablePortal,\n    container: container,\n    children: /*#__PURE__*/_jsx(PopperTooltip, _extends({\n      anchorEl: anchorEl,\n      direction: direction,\n      disablePortal: disablePortal,\n      modifiers: modifiers,\n      ref: forwardedRef,\n      open: transition ? !exited : open,\n      placement: placement,\n      popperOptions: popperOptions,\n      popperRef: popperRef,\n      slotProps: slotProps,\n      slots: slots\n    }, other, {\n      style: _extends({\n        // Prevents scroll issue, waiting for Popper.js to add this style once initiated.\n        position: 'fixed',\n        // Fix Popper.js display issue\n        top: 0,\n        left: 0,\n        display\n      }, style),\n      TransitionProps: transitionProps,\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Popper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * An HTML element, [virtualElement](https://popper.js.org/docs/v2/virtual-elements/),\n   * or a function that returns either.\n   * It's used to set the position of the popper.\n   * The return value will passed as the reference object of the Popper instance.\n   */\n  anchorEl: chainPropTypes(PropTypes.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]), props => {\n    if (props.open) {\n      const resolvedAnchorEl = resolveAnchorEl(props.anchorEl);\n      if (resolvedAnchorEl && isHTMLElement(resolvedAnchorEl) && resolvedAnchorEl.nodeType === 1) {\n        const box = resolvedAnchorEl.getBoundingClientRect();\n        if (process.env.NODE_ENV !== 'test' && box.top === 0 && box.left === 0 && box.right === 0 && box.bottom === 0) {\n          return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'The anchor element should be part of the document layout.', \"Make sure the element is present in the document or that it's not display none.\"].join('\\n'));\n        }\n      } else if (!resolvedAnchorEl || typeof resolvedAnchorEl.getBoundingClientRect !== 'function' || isVirtualElement(resolvedAnchorEl) && resolvedAnchorEl.contextElement != null && resolvedAnchorEl.contextElement.nodeType !== 1) {\n        return new Error(['MUI: The `anchorEl` prop provided to the component is invalid.', 'It should be an HTML element instance or a virtualElement ', '(https://popper.js.org/docs/v2/virtual-elements/).'].join('\\n'));\n      }\n    }\n    return null;\n  }),\n  /**\n   * Popper render function or node.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.node, PropTypes.func]),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * Direction of the text.\n   * @default 'ltr'\n   */\n  direction: PropTypes.oneOf(['ltr', 'rtl']),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Popper.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Popper.js is based on a \"plugin-like\" architecture,\n   * most of its features are fully encapsulated \"modifiers\".\n   *\n   * A modifier is a function that is called each time Popper.js needs to\n   * compute the position of the popper.\n   * For this reason, modifiers should be very performant to avoid bottlenecks.\n   * To learn how to create a modifier, [read the modifiers documentation](https://popper.js.org/docs/v2/modifiers/).\n   */\n  modifiers: PropTypes.arrayOf(PropTypes.shape({\n    data: PropTypes.object,\n    effect: PropTypes.func,\n    enabled: PropTypes.bool,\n    fn: PropTypes.func,\n    name: PropTypes.any,\n    options: PropTypes.object,\n    phase: PropTypes.oneOf(['afterMain', 'afterRead', 'afterWrite', 'beforeMain', 'beforeRead', 'beforeWrite', 'main', 'read', 'write']),\n    requires: PropTypes.arrayOf(PropTypes.string),\n    requiresIfExists: PropTypes.arrayOf(PropTypes.string)\n  })),\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * Popper placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * Options provided to the [`Popper.js`](https://popper.js.org/docs/v2/constructors/#options) instance.\n   * @default {}\n   */\n  popperOptions: PropTypes.shape({\n    modifiers: PropTypes.array,\n    onFirstUpdate: PropTypes.func,\n    placement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n    strategy: PropTypes.oneOf(['absolute', 'fixed'])\n  }),\n  /**\n   * A ref that points to the used popper instance.\n   */\n  popperRef: refType,\n  /**\n   * The props used for each slot inside the Popper.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Popper.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * Help supporting a react-transition-group/Transition component.\n   * @default false\n   */\n  transition: PropTypes.bool\n} : void 0;\nexport default Popper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,iBAAiB,EAAE,YAAY,CAAC;EAC7LC,UAAU,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC;AAC9M,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,EAAEC,eAAe,EAAEC,OAAO,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAClM,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,aAAaA,CAACC,SAAS,EAAEC,SAAS,EAAE;EAC3C,IAAIA,SAAS,KAAK,KAAK,EAAE;IACvB,OAAOD,SAAS;EAClB;EACA,QAAQA,SAAS;IACf,KAAK,YAAY;MACf,OAAO,cAAc;IACvB,KAAK,cAAc;MACjB,OAAO,YAAY;IACrB,KAAK,SAAS;MACZ,OAAO,WAAW;IACpB,KAAK,WAAW;MACd,OAAO,SAAS;IAClB;MACE,OAAOA,SAAS;EACpB;AACF;AACA,SAASE,eAAeA,CAACC,QAAQ,EAAE;EACjC,OAAO,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC,CAAC,GAAGA,QAAQ;AAC/D;AACA,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,QAAQ,KAAKC,SAAS;AACvC;AACA,SAASC,gBAAgBA,CAACH,OAAO,EAAE;EACjC,OAAO,CAACD,aAAa,CAACC,OAAO,CAAC;AAChC;AACA,MAAMI,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOpB,cAAc,CAACmB,KAAK,EAAEhB,qBAAqB,EAAEe,OAAO,CAAC;AAC9D,CAAC;AACD,MAAMG,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAMC,aAAa,GAAG,aAAalC,KAAK,CAACmC,UAAU,CAAC,SAASD,aAAaA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC9F,IAAIC,WAAW;EACf,MAAM;MACFhB,QAAQ;MACRiB,QAAQ;MACRnB,SAAS;MACToB,aAAa;MACbC,SAAS;MACTC,IAAI;MACJvB,SAAS,EAAEwB,gBAAgB;MAC3BC,aAAa;MACbC,SAAS,EAAEC,aAAa;MACxBC,SAAS,GAAG,CAAC,CAAC;MACdhB,KAAK,GAAG,CAAC,CAAC;MACViB;MACA;MACA;IACF,CAAC,GAAGZ,KAAK;IACTa,KAAK,GAAGpD,6BAA6B,CAACuC,KAAK,EAAEtC,SAAS,CAAC;EACzD,MAAMoD,UAAU,GAAGlD,KAAK,CAACmD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMC,MAAM,GAAG3C,UAAU,CAACyC,UAAU,EAAEb,YAAY,CAAC;EACnD,MAAMQ,SAAS,GAAG7C,KAAK,CAACmD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAME,eAAe,GAAG5C,UAAU,CAACoC,SAAS,EAAEC,aAAa,CAAC;EAC5D,MAAMQ,kBAAkB,GAAGtD,KAAK,CAACmD,MAAM,CAACE,eAAe,CAAC;EACxD9C,iBAAiB,CAAC,MAAM;IACtB+C,kBAAkB,CAACC,OAAO,GAAGF,eAAe;EAC9C,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EACrBrD,KAAK,CAACwD,mBAAmB,CAACV,aAAa,EAAE,MAAMD,SAAS,CAACU,OAAO,EAAE,EAAE,CAAC;EACrE,MAAME,YAAY,GAAGvC,aAAa,CAACyB,gBAAgB,EAAEvB,SAAS,CAAC;EAC/D;AACF;AACA;AACA;EACE,MAAM,CAACD,SAAS,EAAEuC,YAAY,CAAC,GAAG1D,KAAK,CAAC2D,QAAQ,CAACF,YAAY,CAAC;EAC9D,MAAM,CAACG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7D,KAAK,CAAC2D,QAAQ,CAACtC,eAAe,CAACC,QAAQ,CAAC,CAAC;EACnGtB,KAAK,CAAC8D,SAAS,CAAC,MAAM;IACpB,IAAIjB,SAAS,CAACU,OAAO,EAAE;MACrBV,SAAS,CAACU,OAAO,CAACQ,WAAW,CAAC,CAAC;IACjC;EACF,CAAC,CAAC;EACF/D,KAAK,CAAC8D,SAAS,CAAC,MAAM;IACpB,IAAIxC,QAAQ,EAAE;MACZuC,wBAAwB,CAACxC,eAAe,CAACC,QAAQ,CAAC,CAAC;IACrD;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACdf,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACqD,qBAAqB,IAAI,CAAClB,IAAI,EAAE;MACnC,OAAOhB,SAAS;IAClB;IACA,MAAMsC,kBAAkB,GAAGC,IAAI,IAAI;MACjCP,YAAY,CAACO,IAAI,CAAC9C,SAAS,CAAC;IAC9B,CAAC;IACD,IAAI+C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIR,qBAAqB,IAAIrC,aAAa,CAACqC,qBAAqB,CAAC,IAAIA,qBAAqB,CAACnC,QAAQ,KAAK,CAAC,EAAE;QACzG,MAAM4C,GAAG,GAAGT,qBAAqB,CAACU,qBAAqB,CAAC,CAAC;QACzD,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIC,GAAG,CAACE,GAAG,KAAK,CAAC,IAAIF,GAAG,CAACG,IAAI,KAAK,CAAC,IAAIH,GAAG,CAACI,KAAK,KAAK,CAAC,IAAIJ,GAAG,CAACK,MAAM,KAAK,CAAC,EAAE;UAC7GC,OAAO,CAACC,IAAI,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7O;MACF;IACF;IACA,IAAIC,eAAe,GAAG,CAAC;MACrBC,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAE;QACPC,WAAW,EAAEzC;MACf;IACF,CAAC,EAAE;MACDuC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE;QACPC,WAAW,EAAEzC;MACf;IACF,CAAC,EAAE;MACDuC,IAAI,EAAE,UAAU;MAChBG,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,YAAY;MACnBC,EAAE,EAAEA,CAAC;QACHC;MACF,CAAC,KAAK;QACJrB,kBAAkB,CAACqB,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC;IACF,IAAI5C,SAAS,IAAI,IAAI,EAAE;MACrBqC,eAAe,GAAGA,eAAe,CAACQ,MAAM,CAAC7C,SAAS,CAAC;IACrD;IACA,IAAIG,aAAa,IAAIA,aAAa,CAACH,SAAS,IAAI,IAAI,EAAE;MACpDqC,eAAe,GAAGA,eAAe,CAACQ,MAAM,CAAC1C,aAAa,CAACH,SAAS,CAAC;IACnE;IACA,MAAM8C,MAAM,GAAG7E,YAAY,CAACkD,qBAAqB,EAAEV,UAAU,CAACK,OAAO,EAAE3D,QAAQ,CAAC;MAC9EuB,SAAS,EAAEsC;IACb,CAAC,EAAEb,aAAa,EAAE;MAChBH,SAAS,EAAEqC;IACb,CAAC,CAAC,CAAC;IACHxB,kBAAkB,CAACC,OAAO,CAACgC,MAAM,CAAC;IAClC,OAAO,MAAM;MACXA,MAAM,CAACC,OAAO,CAAC,CAAC;MAChBlC,kBAAkB,CAACC,OAAO,CAAC,IAAI,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACK,qBAAqB,EAAEpB,aAAa,EAAEC,SAAS,EAAEC,IAAI,EAAEE,aAAa,EAAEa,YAAY,CAAC,CAAC;EACxF,MAAMgC,UAAU,GAAG;IACjBtE,SAAS,EAAEA;EACb,CAAC;EACD,IAAI6B,eAAe,KAAK,IAAI,EAAE;IAC5ByC,UAAU,CAACzC,eAAe,GAAGA,eAAe;EAC9C;EACA,MAAMlB,OAAO,GAAGF,iBAAiB,CAACQ,KAAK,CAAC;EACxC,MAAMsD,IAAI,GAAG,CAACpD,WAAW,GAAGP,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGM,WAAW,GAAG,KAAK;EACrE,MAAMqD,SAAS,GAAG9E,YAAY,CAAC;IAC7B+E,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAE9C,SAAS,CAACf,IAAI;IACjC8D,sBAAsB,EAAE7C,KAAK;IAC7B8C,eAAe,EAAE;MACfC,IAAI,EAAE,SAAS;MACfC,GAAG,EAAE7C;IACP,CAAC;IACDvB,UAAU,EAAEO,KAAK;IACjB8D,SAAS,EAAEpE,OAAO,CAACE;EACrB,CAAC,CAAC;EACF,OAAO,aAAaf,IAAI,CAACyE,IAAI,EAAE9F,QAAQ,CAAC,CAAC,CAAC,EAAE+F,SAAS,EAAE;IACrDpD,QAAQ,EAAE,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACkD,UAAU,CAAC,GAAGlD;EACpE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;;AAEF;AACA;AACA;AACA,MAAM4D,MAAM,GAAG,aAAanG,KAAK,CAACmC,UAAU,CAAC,SAASgE,MAAMA,CAAC/D,KAAK,EAAEC,YAAY,EAAE;EAChF,MAAM;MACFf,QAAQ;MACRiB,QAAQ;MACR6D,SAAS,EAAEC,aAAa;MACxBjF,SAAS,GAAG,KAAK;MACjBoB,aAAa,GAAG,KAAK;MACrB8D,WAAW,GAAG,KAAK;MACnB7D,SAAS;MACTC,IAAI;MACJvB,SAAS,GAAG,QAAQ;MACpByB,aAAa,GAAGX,oBAAoB;MACpCY,SAAS;MACT0D,KAAK;MACLC,UAAU,GAAG,KAAK;MAClBzD,SAAS,GAAG,CAAC,CAAC;MACdhB,KAAK,GAAG,CAAC;IACX,CAAC,GAAGK,KAAK;IACTa,KAAK,GAAGpD,6BAA6B,CAACuC,KAAK,EAAErC,UAAU,CAAC;EAC1D,MAAM,CAAC0G,MAAM,EAAEC,SAAS,CAAC,GAAG1G,KAAK,CAAC2D,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAMgD,WAAW,GAAGA,CAAA,KAAM;IACxBD,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EACD,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBF,SAAS,CAAC,IAAI,CAAC;EACjB,CAAC;EACD,IAAI,CAACJ,WAAW,IAAI,CAAC5D,IAAI,KAAK,CAAC8D,UAAU,IAAIC,MAAM,CAAC,EAAE;IACpD,OAAO,IAAI;EACb;;EAEA;EACA;EACA;EACA,IAAIL,SAAS;EACb,IAAIC,aAAa,EAAE;IACjBD,SAAS,GAAGC,aAAa;EAC3B,CAAC,MAAM,IAAI/E,QAAQ,EAAE;IACnB,MAAMuF,gBAAgB,GAAGxF,eAAe,CAACC,QAAQ,CAAC;IAClD8E,SAAS,GAAGS,gBAAgB,IAAItF,aAAa,CAACsF,gBAAgB,CAAC,GAAGxG,aAAa,CAACwG,gBAAgB,CAAC,CAACC,IAAI,GAAGzG,aAAa,CAAC,IAAI,CAAC,CAACyG,IAAI;EACnI;EACA,MAAMC,OAAO,GAAG,CAACrE,IAAI,IAAI4D,WAAW,KAAK,CAACE,UAAU,IAAIC,MAAM,CAAC,GAAG,MAAM,GAAG/E,SAAS;EACpF,MAAMsF,eAAe,GAAGR,UAAU,GAAG;IACnCS,EAAE,EAAEvE,IAAI;IACRwE,OAAO,EAAEP,WAAW;IACpBQ,QAAQ,EAAEP;EACZ,CAAC,GAAGlF,SAAS;EACb,OAAO,aAAaT,IAAI,CAACH,MAAM,EAAE;IAC/B0B,aAAa,EAAEA,aAAa;IAC5B4D,SAAS,EAAEA,SAAS;IACpB7D,QAAQ,EAAE,aAAatB,IAAI,CAACiB,aAAa,EAAEtC,QAAQ,CAAC;MAClD0B,QAAQ,EAAEA,QAAQ;MAClBF,SAAS,EAAEA,SAAS;MACpBoB,aAAa,EAAEA,aAAa;MAC5BC,SAAS,EAAEA,SAAS;MACpBwD,GAAG,EAAE5D,YAAY;MACjBK,IAAI,EAAE8D,UAAU,GAAG,CAACC,MAAM,GAAG/D,IAAI;MACjCvB,SAAS,EAAEA,SAAS;MACpByB,aAAa,EAAEA,aAAa;MAC5BC,SAAS,EAAEA,SAAS;MACpBE,SAAS,EAAEA,SAAS;MACpBhB,KAAK,EAAEA;IACT,CAAC,EAAEkB,KAAK,EAAE;MACRsD,KAAK,EAAE3G,QAAQ,CAAC;QACd;QACAwH,QAAQ,EAAE,OAAO;QACjB;QACA7C,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPuC;MACF,CAAC,EAAER,KAAK,CAAC;MACTvD,eAAe,EAAEgE,eAAe;MAChCzE,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF2B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG+B,MAAM,CAACkB,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACE/F,QAAQ,EAAErB,cAAc,CAACU,SAAS,CAAC2G,SAAS,CAAC,CAACpH,eAAe,EAAES,SAAS,CAAC4G,MAAM,EAAE5G,SAAS,CAAC6G,IAAI,CAAC,CAAC,EAAEpF,KAAK,IAAI;IAC1G,IAAIA,KAAK,CAACM,IAAI,EAAE;MACd,MAAMmE,gBAAgB,GAAGxF,eAAe,CAACe,KAAK,CAACd,QAAQ,CAAC;MACxD,IAAIuF,gBAAgB,IAAItF,aAAa,CAACsF,gBAAgB,CAAC,IAAIA,gBAAgB,CAACpF,QAAQ,KAAK,CAAC,EAAE;QAC1F,MAAM4C,GAAG,GAAGwC,gBAAgB,CAACvC,qBAAqB,CAAC,CAAC;QACpD,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAIC,GAAG,CAACE,GAAG,KAAK,CAAC,IAAIF,GAAG,CAACG,IAAI,KAAK,CAAC,IAAIH,GAAG,CAACI,KAAK,KAAK,CAAC,IAAIJ,GAAG,CAACK,MAAM,KAAK,CAAC,EAAE;UAC7G,OAAO,IAAI+C,KAAK,CAAC,CAAC,gEAAgE,EAAE,2DAA2D,EAAE,iFAAiF,CAAC,CAAC5C,IAAI,CAAC,IAAI,CAAC,CAAC;QACjP;MACF,CAAC,MAAM,IAAI,CAACgC,gBAAgB,IAAI,OAAOA,gBAAgB,CAACvC,qBAAqB,KAAK,UAAU,IAAI3C,gBAAgB,CAACkF,gBAAgB,CAAC,IAAIA,gBAAgB,CAACa,cAAc,IAAI,IAAI,IAAIb,gBAAgB,CAACa,cAAc,CAACjG,QAAQ,KAAK,CAAC,EAAE;QAC/N,OAAO,IAAIgG,KAAK,CAAC,CAAC,gEAAgE,EAAE,4DAA4D,EAAE,oDAAoD,CAAC,CAAC5C,IAAI,CAAC,IAAI,CAAC,CAAC;MACrN;IACF;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;EACEtC,QAAQ,EAAE5B,SAAS,CAAC,sCAAsC2G,SAAS,CAAC,CAAC3G,SAAS,CAACgH,IAAI,EAAEhH,SAAS,CAAC6G,IAAI,CAAC,CAAC;EACrG;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEpB,SAAS,EAAEzF,SAAS,CAAC,sCAAsC2G,SAAS,CAAC,CAACpH,eAAe,EAAES,SAAS,CAAC6G,IAAI,CAAC,CAAC;EACvG;AACF;AACA;AACA;EACEpG,SAAS,EAAET,SAAS,CAACiH,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAC1C;AACF;AACA;AACA;EACEpF,aAAa,EAAE7B,SAAS,CAACkH,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;EACEvB,WAAW,EAAE3F,SAAS,CAACkH,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEpF,SAAS,EAAE9B,SAAS,CAACmH,OAAO,CAACnH,SAAS,CAACoH,KAAK,CAAC;IAC3C9D,IAAI,EAAEtD,SAAS,CAAC4G,MAAM;IACtBS,MAAM,EAAErH,SAAS,CAAC6G,IAAI;IACtBtC,OAAO,EAAEvE,SAAS,CAACkH,IAAI;IACvBzC,EAAE,EAAEzE,SAAS,CAAC6G,IAAI;IAClBzC,IAAI,EAAEpE,SAAS,CAACsH,GAAG;IACnBjD,OAAO,EAAErE,SAAS,CAAC4G,MAAM;IACzBpC,KAAK,EAAExE,SAAS,CAACiH,KAAK,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACpIM,QAAQ,EAAEvH,SAAS,CAACmH,OAAO,CAACnH,SAAS,CAACwH,MAAM,CAAC;IAC7CC,gBAAgB,EAAEzH,SAAS,CAACmH,OAAO,CAACnH,SAAS,CAACwH,MAAM;EACtD,CAAC,CAAC,CAAC;EACH;AACF;AACA;EACEzF,IAAI,EAAE/B,SAAS,CAACkH,IAAI,CAACQ,UAAU;EAC/B;AACF;AACA;AACA;EACElH,SAAS,EAAER,SAAS,CAACiH,KAAK,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;EAC5M;AACF;AACA;AACA;EACEhF,aAAa,EAAEjC,SAAS,CAACoH,KAAK,CAAC;IAC7BtF,SAAS,EAAE9B,SAAS,CAAC2H,KAAK;IAC1BC,aAAa,EAAE5H,SAAS,CAAC6G,IAAI;IAC7BrG,SAAS,EAAER,SAAS,CAACiH,KAAK,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAC5MY,QAAQ,EAAE7H,SAAS,CAACiH,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC;EACjD,CAAC,CAAC;EACF;AACF;AACA;EACE/E,SAAS,EAAE1C,OAAO;EAClB;AACF;AACA;AACA;EACE4C,SAAS,EAAEpC,SAAS,CAACoH,KAAK,CAAC;IACzB/F,IAAI,EAAErB,SAAS,CAAC2G,SAAS,CAAC,CAAC3G,SAAS,CAAC6G,IAAI,EAAE7G,SAAS,CAAC4G,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACExF,KAAK,EAAEpB,SAAS,CAACoH,KAAK,CAAC;IACrB/F,IAAI,EAAErB,SAAS,CAACiF;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEY,UAAU,EAAE7F,SAAS,CAACkH;AACxB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe1B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}