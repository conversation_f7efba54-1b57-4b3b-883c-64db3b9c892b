{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.75 8H11v5H7.75v1h.75c1.38 0 2.5 1.12 2.5 2.5S9.88 19 8.5 19h-.75v4h-1.5v-4H5.5C4.12 19 3 17.88 3 16.5S4.12 14 5.5 14h.75v-1H3V8h3.25V7H5.5C4.12 7 3 5.88 3 4.5S4.12 2 5.5 2h.75V1h1.5v1h.75C9.88 2 11 3.12 11 4.5S9.88 7 8.5 7h-.75zm10-1h.75C19.88 7 21 5.88 21 4.5S19.88 2 18.5 2h-.75V1h-1.5v1h-.75C14.12 2 13 3.12 13 4.5S14.12 7 15.5 7h.75v1H13v5h3.25v1h-.75c-1.38 0-2.5 1.12-2.5 2.5s1.12 2.5 2.5 2.5h.75v4h1.5v-4h.75c1.38 0 2.5-1.12 2.5-2.5S19.88 14 18.5 14h-.75v-1H21V8h-3.25z\"\n}), 'KebabDining');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/icons-material/esm/KebabDining.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.75 8H11v5H7.75v1h.75c1.38 0 2.5 1.12 2.5 2.5S9.88 19 8.5 19h-.75v4h-1.5v-4H5.5C4.12 19 3 17.88 3 16.5S4.12 14 5.5 14h.75v-1H3V8h3.25V7H5.5C4.12 7 3 5.88 3 4.5S4.12 2 5.5 2h.75V1h1.5v1h.75C9.88 2 11 3.12 11 4.5S9.88 7 8.5 7h-.75zm10-1h.75C19.88 7 21 5.88 21 4.5S19.88 2 18.5 2h-.75V1h-1.5v1h-.75C14.12 2 13 3.12 13 4.5S14.12 7 15.5 7h.75v1H13v5h3.25v1h-.75c-1.38 0-2.5 1.12-2.5 2.5s1.12 2.5 2.5 2.5h.75v4h1.5v-4h.75c1.38 0 2.5-1.12 2.5-2.5S19.88 14 18.5 14h-.75v-1H21V8h-3.25z\"\n}), 'KebabDining');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}