{"ast": null, "code": "import assign from \"../assign/index.js\";\nexport default function cloneObject(object) {\n  return assign({}, object);\n}", "map": {"version": 3, "names": ["assign", "cloneObject", "object"], "sources": ["C:/tictetmigration/frontend/node_modules/date-fns/esm/_lib/cloneObject/index.js"], "sourcesContent": ["import assign from \"../assign/index.js\";\nexport default function cloneObject(object) {\n  return assign({}, object);\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,eAAe,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC1C,OAAOF,MAAM,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}