{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport setRef from '../setRef';\nexport default function useForkRef(...refs) {\n  /**\n   * This will create a new function if the refs passed to this hook change and are all defined.\n   * This means react will call the old forkRef with `null` and the new forkRef\n   * with the ref. Cleanup naturally emerges from this behavior.\n   */\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return instance => {\n      refs.forEach(ref => {\n        setRef(ref, instance);\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}", "map": {"version": 3, "names": ["React", "setRef", "useForkRef", "refs", "useMemo", "every", "ref", "instance", "for<PERSON>ach"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/utils/esm/useForkRef/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport setRef from '../setRef';\nexport default function useForkRef(...refs) {\n  /**\n   * This will create a new function if the refs passed to this hook change and are all defined.\n   * This means react will call the old forkRef with `null` and the new forkRef\n   * with the ref. Cleanup naturally emerges from this behavior.\n   */\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return instance => {\n      refs.forEach(ref => {\n        setRef(ref, instance);\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,eAAe,SAASC,UAAUA,CAAC,GAAGC,IAAI,EAAE;EAC1C;AACF;AACA;AACA;AACA;EACE,OAAOH,KAAK,CAACI,OAAO,CAAC,MAAM;IACzB,IAAID,IAAI,CAACE,KAAK,CAACC,GAAG,IAAIA,GAAG,IAAI,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI;IACb;IACA,OAAOC,QAAQ,IAAI;MACjBJ,IAAI,CAACK,OAAO,CAACF,GAAG,IAAI;QAClBL,MAAM,CAACK,GAAG,EAAEC,QAAQ,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD;EACF,CAAC,EAAEJ,IAAI,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}