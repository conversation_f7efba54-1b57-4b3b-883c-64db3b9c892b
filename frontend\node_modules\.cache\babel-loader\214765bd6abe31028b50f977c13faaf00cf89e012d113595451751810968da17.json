{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"children\", \"className\", \"defaultValue\", \"name\", \"onChange\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FormGroup from '../FormGroup';\nimport { getRadioGroupUtilityClass } from './radioGroupClasses';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport RadioGroupContext from './RadioGroupContext';\nimport useId from '../utils/useId';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = props => {\n  const {\n    classes,\n    row,\n    error\n  } = props;\n  const slots = {\n    root: ['root', row && 'row', error && 'error']\n  };\n  return composeClasses(slots, getRadioGroupUtilityClass, classes);\n};\nconst RadioGroup = /*#__PURE__*/React.forwardRef(function RadioGroup(props, ref) {\n  const {\n      // private\n      // eslint-disable-next-line react/prop-types\n      actions,\n      children,\n      className,\n      defaultValue,\n      name: nameProp,\n      onChange,\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootRef = React.useRef(null);\n  const classes = useUtilityClasses(props);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'RadioGroup'\n  });\n  React.useImperativeHandle(actions, () => ({\n    focus: () => {\n      let input = rootRef.current.querySelector('input:not(:disabled):checked');\n      if (!input) {\n        input = rootRef.current.querySelector('input:not(:disabled)');\n      }\n      if (input) {\n        input.focus();\n      }\n    }\n  }), []);\n  const handleRef = useForkRef(ref, rootRef);\n  const name = useId(nameProp);\n  const contextValue = React.useMemo(() => ({\n    name,\n    onChange(event) {\n      setValueState(event.target.value);\n      if (onChange) {\n        onChange(event, event.target.value);\n      }\n    },\n    value\n  }), [name, onChange, setValueState, value]);\n  return /*#__PURE__*/_jsx(RadioGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(FormGroup, _extends({\n      role: \"radiogroup\",\n      ref: handleRef,\n      className: clsx(classes.root, className)\n    }, other, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? RadioGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * The name used to reference the value of the control.\n   * If you don't provide this prop, it falls back to a randomly generated name.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when a radio button is selected.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * @param {string} value The value of the selected radio button.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * Value of the selected radio button. The DOM API casts this to a string.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default RadioGroup;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "FormGroup", "getRadioGroupUtilityClass", "useForkRef", "useControlled", "RadioGroupContext", "useId", "jsx", "_jsx", "useUtilityClasses", "props", "classes", "row", "error", "slots", "root", "RadioGroup", "forwardRef", "ref", "actions", "children", "className", "defaultValue", "name", "nameProp", "onChange", "value", "valueProp", "other", "rootRef", "useRef", "setValueState", "controlled", "default", "useImperativeHandle", "focus", "input", "current", "querySelector", "handleRef", "contextValue", "useMemo", "event", "target", "Provider", "role", "process", "env", "NODE_ENV", "propTypes", "node", "string", "any", "func"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/RadioGroup/RadioGroup.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"children\", \"className\", \"defaultValue\", \"name\", \"onChange\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport FormGroup from '../FormGroup';\nimport { getRadioGroupUtilityClass } from './radioGroupClasses';\nimport useForkRef from '../utils/useForkRef';\nimport useControlled from '../utils/useControlled';\nimport RadioGroupContext from './RadioGroupContext';\nimport useId from '../utils/useId';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = props => {\n  const {\n    classes,\n    row,\n    error\n  } = props;\n  const slots = {\n    root: ['root', row && 'row', error && 'error']\n  };\n  return composeClasses(slots, getRadioGroupUtilityClass, classes);\n};\nconst RadioGroup = /*#__PURE__*/React.forwardRef(function RadioGroup(props, ref) {\n  const {\n      // private\n      // eslint-disable-next-line react/prop-types\n      actions,\n      children,\n      className,\n      defaultValue,\n      name: nameProp,\n      onChange,\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootRef = React.useRef(null);\n  const classes = useUtilityClasses(props);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'RadioGroup'\n  });\n  React.useImperativeHandle(actions, () => ({\n    focus: () => {\n      let input = rootRef.current.querySelector('input:not(:disabled):checked');\n      if (!input) {\n        input = rootRef.current.querySelector('input:not(:disabled)');\n      }\n      if (input) {\n        input.focus();\n      }\n    }\n  }), []);\n  const handleRef = useForkRef(ref, rootRef);\n  const name = useId(nameProp);\n  const contextValue = React.useMemo(() => ({\n    name,\n    onChange(event) {\n      setValueState(event.target.value);\n      if (onChange) {\n        onChange(event, event.target.value);\n      }\n    },\n    value\n  }), [name, onChange, setValueState, value]);\n  return /*#__PURE__*/_jsx(RadioGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(FormGroup, _extends({\n      role: \"radiogroup\",\n      ref: handleRef,\n      className: clsx(classes.root, className)\n    }, other, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? RadioGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * The name used to reference the value of the control.\n   * If you don't provide this prop, it falls back to a randomly generated name.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when a radio button is selected.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * @param {string} value The value of the selected radio button.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   */\n  onChange: PropTypes.func,\n  /**\n   * Value of the selected radio button. The DOM API casts this to a string.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default RadioGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;AACnG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,KAAK,MAAM,gBAAgB;AAClC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACjC,MAAM;IACJC,OAAO;IACPC,GAAG;IACHC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,GAAG,IAAI,KAAK,EAAEC,KAAK,IAAI,OAAO;EAC/C,CAAC;EACD,OAAOb,cAAc,CAACc,KAAK,EAAEZ,yBAAyB,EAAES,OAAO,CAAC;AAClE,CAAC;AACD,MAAMK,UAAU,GAAG,aAAanB,KAAK,CAACoB,UAAU,CAAC,SAASD,UAAUA,CAACN,KAAK,EAAEQ,GAAG,EAAE;EAC/E,MAAM;MACF;MACA;MACAC,OAAO;MACPC,QAAQ;MACRC,SAAS;MACTC,YAAY;MACZC,IAAI,EAAEC,QAAQ;MACdC,QAAQ;MACRC,KAAK,EAAEC;IACT,CAAC,GAAGjB,KAAK;IACTkB,KAAK,GAAGjC,6BAA6B,CAACe,KAAK,EAAEd,SAAS,CAAC;EACzD,MAAMiC,OAAO,GAAGhC,KAAK,CAACiC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMnB,OAAO,GAAGF,iBAAiB,CAACC,KAAK,CAAC;EACxC,MAAM,CAACgB,KAAK,EAAEK,aAAa,CAAC,GAAG3B,aAAa,CAAC;IAC3C4B,UAAU,EAAEL,SAAS;IACrBM,OAAO,EAAEX,YAAY;IACrBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF1B,KAAK,CAACqC,mBAAmB,CAACf,OAAO,EAAE,OAAO;IACxCgB,KAAK,EAAEA,CAAA,KAAM;MACX,IAAIC,KAAK,GAAGP,OAAO,CAACQ,OAAO,CAACC,aAAa,CAAC,8BAA8B,CAAC;MACzE,IAAI,CAACF,KAAK,EAAE;QACVA,KAAK,GAAGP,OAAO,CAACQ,OAAO,CAACC,aAAa,CAAC,sBAAsB,CAAC;MAC/D;MACA,IAAIF,KAAK,EAAE;QACTA,KAAK,CAACD,KAAK,CAAC,CAAC;MACf;IACF;EACF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAMI,SAAS,GAAGpC,UAAU,CAACe,GAAG,EAAEW,OAAO,CAAC;EAC1C,MAAMN,IAAI,GAAGjB,KAAK,CAACkB,QAAQ,CAAC;EAC5B,MAAMgB,YAAY,GAAG3C,KAAK,CAAC4C,OAAO,CAAC,OAAO;IACxClB,IAAI;IACJE,QAAQA,CAACiB,KAAK,EAAE;MACdX,aAAa,CAACW,KAAK,CAACC,MAAM,CAACjB,KAAK,CAAC;MACjC,IAAID,QAAQ,EAAE;QACZA,QAAQ,CAACiB,KAAK,EAAEA,KAAK,CAACC,MAAM,CAACjB,KAAK,CAAC;MACrC;IACF,CAAC;IACDA;EACF,CAAC,CAAC,EAAE,CAACH,IAAI,EAAEE,QAAQ,EAAEM,aAAa,EAAEL,KAAK,CAAC,CAAC;EAC3C,OAAO,aAAalB,IAAI,CAACH,iBAAiB,CAACuC,QAAQ,EAAE;IACnDlB,KAAK,EAAEc,YAAY;IACnBpB,QAAQ,EAAE,aAAaZ,IAAI,CAACP,SAAS,EAAEP,QAAQ,CAAC;MAC9CmD,IAAI,EAAE,YAAY;MAClB3B,GAAG,EAAEqB,SAAS;MACdlB,SAAS,EAAEtB,IAAI,CAACY,OAAO,CAACI,IAAI,EAAEM,SAAS;IACzC,CAAC,EAAEO,KAAK,EAAE;MACRR,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF0B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhC,UAAU,CAACiC,SAAS,CAAC,yBAAyB;EACpF;EACA;EACA;EACA;EACA;AACF;AACA;EACE7B,QAAQ,EAAEtB,SAAS,CAACoD,IAAI;EACxB;AACF;AACA;EACE7B,SAAS,EAAEvB,SAAS,CAACqD,MAAM;EAC3B;AACF;AACA;EACE7B,YAAY,EAAExB,SAAS,CAACsD,GAAG;EAC3B;AACF;AACA;AACA;EACE7B,IAAI,EAAEzB,SAAS,CAACqD,MAAM;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACE1B,QAAQ,EAAE3B,SAAS,CAACuD,IAAI;EACxB;AACF;AACA;EACE3B,KAAK,EAAE5B,SAAS,CAACsD;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAepC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}