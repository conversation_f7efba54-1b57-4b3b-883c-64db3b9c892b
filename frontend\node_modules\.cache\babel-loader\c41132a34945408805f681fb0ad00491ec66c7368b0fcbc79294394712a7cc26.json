{"ast": null, "code": "export default function formControlState({\n  props,\n  states,\n  muiFormControl\n}) {\n  return states.reduce((acc, state) => {\n    acc[state] = props[state];\n    if (muiFormControl) {\n      if (typeof props[state] === 'undefined') {\n        acc[state] = muiFormControl[state];\n      }\n    }\n    return acc;\n  }, {});\n}", "map": {"version": 3, "names": ["formControlState", "props", "states", "muiFormControl", "reduce", "acc", "state"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/FormControl/formControlState.js"], "sourcesContent": ["export default function formControlState({\n  props,\n  states,\n  muiFormControl\n}) {\n  return states.reduce((acc, state) => {\n    acc[state] = props[state];\n    if (muiFormControl) {\n      if (typeof props[state] === 'undefined') {\n        acc[state] = muiFormControl[state];\n      }\n    }\n    return acc;\n  }, {});\n}"], "mappings": "AAAA,eAAe,SAASA,gBAAgBA,CAAC;EACvCC,KAAK;EACLC,MAAM;EACNC;AACF,CAAC,EAAE;EACD,OAAOD,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IACnCD,GAAG,CAACC,KAAK,CAAC,GAAGL,KAAK,CAACK,KAAK,CAAC;IACzB,IAAIH,cAAc,EAAE;MAClB,IAAI,OAAOF,KAAK,CAACK,KAAK,CAAC,KAAK,WAAW,EAAE;QACvCD,GAAG,CAACC,KAAK,CAAC,GAAGH,cAAc,CAACG,KAAK,CAAC;MACpC;IACF;IACA,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}