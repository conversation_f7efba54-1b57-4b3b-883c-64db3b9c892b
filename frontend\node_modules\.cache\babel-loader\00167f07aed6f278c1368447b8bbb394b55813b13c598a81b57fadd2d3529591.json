{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getRadioGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiRadioGroup', slot);\n}\nconst radioGroupClasses = generateUtilityClasses('MuiRadioGroup', ['root', 'row', 'error']);\nexport default radioGroupClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getRadioGroupUtilityClass", "slot", "radioGroupClasses"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/RadioGroup/radioGroupClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getRadioGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiRadioGroup', slot);\n}\nconst radioGroupClasses = generateUtilityClasses('MuiRadioGroup', ['root', 'row', 'error']);\nexport default radioGroupClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOF,oBAAoB,CAAC,eAAe,EAAEE,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC3F,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}