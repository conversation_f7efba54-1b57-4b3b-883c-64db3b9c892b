{"ast": null, "code": "export { default } from './elementTypeAcceptingRef';", "map": {"version": 3, "names": ["default"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/utils/esm/elementTypeAcceptingRef/index.js"], "sourcesContent": ["export { default } from './elementTypeAcceptingRef';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}