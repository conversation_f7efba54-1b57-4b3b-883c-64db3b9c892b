{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getInputLabelUtilityClasses(slot) {\n  return generateUtilityClass('MuiInputLabel', slot);\n}\nconst inputLabelClasses = generateUtilityClasses('MuiInputLabel', ['root', 'focused', 'disabled', 'error', 'required', 'asterisk', 'formControl', 'sizeSmall', 'shrink', 'animated', 'standard', 'filled', 'outlined']);\nexport default inputLabelClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getInputLabelUtilityClasses", "slot", "inputLabelClasses"], "sources": ["C:/tictetmigration/frontend/node_modules/@mui/material/InputLabel/inputLabelClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getInputLabelUtilityClasses(slot) {\n  return generateUtilityClass('MuiInputLabel', slot);\n}\nconst inputLabelClasses = generateUtilityClasses('MuiInputLabel', ['root', 'focused', 'disabled', 'error', 'required', 'asterisk', 'formControl', 'sizeSmall', 'shrink', 'animated', 'standard', 'filled', 'outlined']);\nexport default inputLabelClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOF,oBAAoB,CAAC,eAAe,EAAEE,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;AACvN,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}